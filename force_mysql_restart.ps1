# Force MySQL Restart PowerShell Script
Write-Host "🔧 Force MySQL Restart Script" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  This script needs to run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host "✅ Running as Administrator" -ForegroundColor Green
Write-Host ""

Write-Host "1. Stopping MySQL services..." -ForegroundColor Cyan
try {
    Stop-Service -Name "mysql" -Force -ErrorAction SilentlyContinue
    Stop-Service -Name "MySQL80" -Force -ErrorAction SilentlyContinue
    Stop-Service -Name "MySQL57" -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Services stopped" -ForegroundColor Green
} catch {
    Write-Host "ℹ️  No MySQL services were running" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "2. Killing MySQL processes..." -ForegroundColor Cyan
try {
    Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "mysql" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✅ MySQL processes terminated" -ForegroundColor Green
} catch {
    Write-Host "ℹ️  No MySQL processes were running" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "3. Waiting 3 seconds..." -ForegroundColor Cyan
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "4. Checking port 3306..." -ForegroundColor Cyan
$portCheck = netstat -an | Select-String ":3306"
if ($portCheck) {
    Write-Host "⚠️  Port 3306 is still in use:" -ForegroundColor Red
    $portCheck | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
    Write-Host ""
    Write-Host "🔄 You may need to restart your computer to fully clear the port" -ForegroundColor Yellow
} else {
    Write-Host "✅ Port 3306 is now free!" -ForegroundColor Green
}

Write-Host ""
Write-Host "5. Removing problematic MySQL files..." -ForegroundColor Cyan
$mysqlDataPath = "C:\xampp\mysql\data"
$logFiles = @("$mysqlDataPath\ib_logfile0", "$mysqlDataPath\ib_logfile1")

foreach ($logFile in $logFiles) {
    if (Test-Path $logFile) {
        try {
            Remove-Item $logFile -Force
            Write-Host "✅ Removed: $(Split-Path $logFile -Leaf)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to remove: $(Split-Path $logFile -Leaf)" -ForegroundColor Red
        }
    } else {
        Write-Host "ℹ️  File not found: $(Split-Path $logFile -Leaf)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎯 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "==============" -ForegroundColor Yellow
Write-Host "1. Close XAMPP Control Panel completely" -ForegroundColor White
Write-Host "2. Right-click XAMPP Control Panel → 'Run as administrator'" -ForegroundColor White
Write-Host "3. Try starting MySQL" -ForegroundColor White
Write-Host ""
Write-Host "If MySQL still won't start, restart your computer first." -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter to exit"

#!/usr/bin/env python3
"""
Modern AI Text Detection Engine
Enhanced with latest OpenAI API, better error handling, and comprehensive analytics
"""

import sys
import os
import logging
import re
import json
import time
import hashlib
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Tuple, Dict, Optional, List
from dotenv import load_dotenv
from openai import OpenAI
import random

# Load environment variables
load_dotenv()

@dataclass
class AnalysisResult:
    """Data class for analysis results"""
    classification: str
    confidence: float
    sub_scores: Dict[str, float]
    reasoning: str
    processing_time: float
    model_used: str
    tokens_used: int = 0
    cost_estimate: float = 0.0
    session_id: str = ""
    
    def to_dict(self):
        return asdict(self)

class ModernAITextDetector:
    """
    Modern AI Text Detection Engine
    Enhanced with better error handling, caching, and analytics
    """
    
    def __init__(self):
        # Initialize OpenAI client
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("Error: No API key provided. Please set the OPENAI_API_KEY environment variable.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = "gpt-3.5-turbo"
        
        # Setup logging
        self.setup_logging()
        
        # Analysis cache
        self.cache = {}
        
        # Session tracking
        self.session_id = self.generate_session_id()
        
        self.logger.info(f"AI Text Detector initialized with session ID: {self.session_id}")
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, 'ai_detector.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def generate_session_id(self) -> str:
        """Generate unique session ID"""
        timestamp = str(int(time.time()))
        random_part = str(random.randint(1000, 9999))
        return f"session_{timestamp}_{random_part}"
    
    def get_text_hash(self, text: str) -> str:
        """Generate hash for text caching"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for analysis"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might interfere
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', text)
        
        return text
    
    def analyze_text_patterns(self, text: str) -> Dict[str, float]:
        """Analyze text patterns for AI detection indicators"""
        patterns = {
            'repetitive_phrases': 0.0,
            'formal_language': 0.0,
            'structured_format': 0.0,
            'vocabulary_complexity': 0.0,
            'sentence_uniformity': 0.0
        }
        
        sentences = text.split('.')
        words = text.split()
        
        if len(sentences) > 1:
            # Check sentence length uniformity
            lengths = [len(s.split()) for s in sentences if s.strip()]
            if lengths:
                avg_length = sum(lengths) / len(lengths)
                variance = sum((l - avg_length) ** 2 for l in lengths) / len(lengths)
                patterns['sentence_uniformity'] = min(1.0, variance / 100)
        
        # Check for formal language indicators
        formal_words = ['furthermore', 'moreover', 'consequently', 'therefore', 'additionally']
        formal_count = sum(1 for word in words if word.lower() in formal_words)
        patterns['formal_language'] = min(1.0, formal_count / max(1, len(words) / 50))
        
        # Check vocabulary complexity
        unique_words = len(set(words))
        if len(words) > 0:
            patterns['vocabulary_complexity'] = unique_words / len(words)
        
        return patterns
    
    def detect_ai_text(self, text: str) -> AnalysisResult:
        """
        Main detection method using OpenAI API
        """
        start_time = time.time()
        
        try:
            # Check cache first
            text_hash = self.get_text_hash(text)
            if text_hash in self.cache:
                self.logger.info("Returning cached result")
                cached_result = self.cache[text_hash]
                cached_result.session_id = self.session_id
                return cached_result
            
            # Preprocess text
            processed_text = self.preprocess_text(text)
            
            # Analyze patterns
            pattern_scores = self.analyze_text_patterns(processed_text)
            
            # Prepare prompt for OpenAI
            prompt = self.create_detection_prompt(processed_text)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert AI text detector. Analyze the given text and determine if it was likely generated by AI."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            # Parse response
            ai_response = response.choices[0].message.content
            
            # Extract classification and confidence
            classification, confidence, reasoning = self.parse_ai_response(ai_response)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create result
            result = AnalysisResult(
                classification=classification,
                confidence=confidence,
                sub_scores=pattern_scores,
                reasoning=reasoning,
                processing_time=processing_time,
                model_used=self.model,
                tokens_used=response.usage.total_tokens,
                cost_estimate=self.calculate_cost(response.usage.total_tokens),
                session_id=self.session_id
            )
            
            # Cache result
            self.cache[text_hash] = result
            
            self.logger.info(f"Analysis completed: {classification} ({confidence:.2f})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in AI detection: {str(e)}")
            
            # Fallback to pattern-based analysis
            return self.fallback_analysis(text, start_time)
    
    def create_detection_prompt(self, text: str) -> str:
        """Create optimized prompt for AI detection"""
        return f"""
        Analyze the following text and determine if it was likely generated by AI or written by a human.
        
        Consider these factors:
        1. Writing style and tone consistency
        2. Vocabulary usage and complexity
        3. Sentence structure patterns
        4. Content flow and coherence
        5. Presence of AI-typical phrases or patterns
        
        Text to analyze:
        "{text}"
        
        Provide your analysis in this format:
        CLASSIFICATION: [AI_GENERATED or HUMAN_WRITTEN]
        CONFIDENCE: [0.0 to 1.0]
        REASONING: [Brief explanation of your decision]
        """
    
    def parse_ai_response(self, response: str) -> Tuple[str, float, str]:
        """Parse OpenAI response to extract classification, confidence, and reasoning"""
        try:
            # Extract classification
            classification_match = re.search(r'CLASSIFICATION:\s*(AI_GENERATED|HUMAN_WRITTEN)', response, re.IGNORECASE)
            classification = classification_match.group(1).upper() if classification_match else "UNCERTAIN"
            
            # Extract confidence
            confidence_match = re.search(r'CONFIDENCE:\s*([\d\.]+)', response)
            confidence = float(confidence_match.group(1)) if confidence_match else 0.5
            
            # Extract reasoning
            reasoning_match = re.search(r'REASONING:\s*(.+)', response, re.DOTALL)
            reasoning = reasoning_match.group(1).strip() if reasoning_match else "No reasoning provided"
            
            return classification, confidence, reasoning
            
        except Exception as e:
            self.logger.error(f"Error parsing AI response: {str(e)}")
            return "UNCERTAIN", 0.5, "Error parsing response"
    
    def fallback_analysis(self, text: str, start_time: float) -> AnalysisResult:
        """Fallback analysis when OpenAI API is unavailable"""
        self.logger.warning("Using fallback analysis method")
        
        pattern_scores = self.analyze_text_patterns(text)
        
        # Simple heuristic-based classification
        avg_score = sum(pattern_scores.values()) / len(pattern_scores)
        
        if avg_score > 0.7:
            classification = "AI_GENERATED"
            confidence = min(0.8, avg_score)
        elif avg_score < 0.3:
            classification = "HUMAN_WRITTEN"
            confidence = min(0.8, 1 - avg_score)
        else:
            classification = "UNCERTAIN"
            confidence = 0.5
        
        reasoning = f"Fallback analysis based on pattern scores: {pattern_scores}"
        
        return AnalysisResult(
            classification=classification,
            confidence=confidence,
            sub_scores=pattern_scores,
            reasoning=reasoning,
            processing_time=time.time() - start_time,
            model_used="fallback_heuristic",
            session_id=self.session_id
        )
    
    def calculate_cost(self, tokens: int) -> float:
        """Calculate estimated cost based on token usage"""
        # GPT-3.5-turbo pricing (approximate)
        cost_per_1k_tokens = 0.002
        return (tokens / 1000) * cost_per_1k_tokens

def main():
    """Main function for command-line usage"""
    if len(sys.argv) != 2:
        print("Usage: python modern_detector.py <text_to_analyze>")
        sys.exit(1)
    
    text = sys.argv[1]
    
    try:
        detector = ModernAITextDetector()
        result = detector.detect_ai_text(text)
        
        # Output JSON result
        print(json.dumps(result.to_dict(), indent=2))
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "classification": "ERROR",
            "confidence": 0.0,
            "reasoning": f"Analysis failed: {str(e)}"
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()

<?php
/**
 * AI Analysis Service
 * Robust PHP wrapper for Python AI detection with proper error handling
 */

class AIAnalysisService {
    private $pythonPath;
    private $scriptPath;
    private $logPath;
    
    public function __construct() {
        $this->pythonPath = $this->findPythonExecutable();
        $this->scriptPath = __DIR__ . '/ai_engine/modern_detector.py';
        $this->logPath = __DIR__ . '/logs/ai_analysis.log';
        
        // Ensure logs directory exists
        if (!is_dir(__DIR__ . '/logs')) {
            mkdir(__DIR__ . '/logs', 0755, true);
        }
    }
    
    /**
     * Find Python executable
     */
    private function findPythonExecutable() {
        $possiblePaths = ['python', 'python3', 'py'];
        
        foreach ($possiblePaths as $path) {
            $output = shell_exec("$path --version 2>&1");
            if ($output && strpos(strtolower($output), 'python') !== false) {
                return $path;
            }
        }
        
        return 'python'; // Default fallback
    }
    
    /**
     * Check if Python and required packages are available
     */
    public function checkDependencies() {
        $issues = [];
        
        // Check Python
        $pythonCheck = shell_exec("{$this->pythonPath} --version 2>&1");
        if (!$pythonCheck || strpos(strtolower($pythonCheck), 'python') === false) {
            $issues[] = "Python not found or not accessible";
        }
        
        // Check if script exists
        if (!file_exists($this->scriptPath)) {
            $issues[] = "AI detection script not found: " . $this->scriptPath;
        }
        
        return $issues;
    }
    
    /**
     * Analyze text using Python AI detector
     */
    public function analyzeText($text) {
        try {
            // Validate input
            if (empty(trim($text))) {
                throw new Exception("Text cannot be empty");
            }
            
            // Check dependencies first
            $issues = $this->checkDependencies();
            if (!empty($issues)) {
                return $this->fallbackAnalysis($text, $issues);
            }
            
            // Escape text for command line
            $escapedText = escapeshellarg($text);
            
            // Build command
            $command = "{$this->pythonPath} \"{$this->scriptPath}\" {$escapedText} 2>&1";
            
            // Log the analysis attempt
            $this->logAnalysis("Starting analysis", ['text_length' => strlen($text)]);
            
            // Execute Python script
            $startTime = microtime(true);
            $output = shell_exec($command);
            $executionTime = microtime(true) - $startTime;
            
            if ($output === null) {
                throw new Exception("Failed to execute Python script");
            }
            
            // Parse JSON output
            $result = json_decode($output, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON response from Python script: " . $output);
            }
            
            // Check for errors in result
            if (isset($result['error'])) {
                throw new Exception("Python script error: " . $result['error']);
            }
            
            // Add execution metadata
            $result['php_execution_time'] = $executionTime;
            $result['timestamp'] = date('c');
            
            // Log successful analysis
            $this->logAnalysis("Analysis completed", [
                'classification' => $result['classification'] ?? 'unknown',
                'confidence' => $result['confidence'] ?? 0,
                'execution_time' => $executionTime
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            // Log error
            $this->logAnalysis("Analysis failed", ['error' => $e->getMessage()]);
            
            // Return fallback analysis
            return $this->fallbackAnalysis($text, [$e->getMessage()]);
        }
    }
    
    /**
     * Fallback analysis when Python script fails
     */
    private function fallbackAnalysis($text, $errors = []) {
        $startTime = microtime(true);
        
        // Simple pattern-based analysis
        $patterns = $this->analyzeTextPatterns($text);
        
        // Calculate overall score
        $avgScore = array_sum($patterns) / count($patterns);
        
        // Determine classification
        if ($avgScore > 0.7) {
            $classification = "AI_GENERATED";
            $confidence = min(0.8, $avgScore);
        } elseif ($avgScore < 0.3) {
            $classification = "HUMAN_WRITTEN";
            $confidence = min(0.8, 1 - $avgScore);
        } else {
            $classification = "UNCERTAIN";
            $confidence = 0.5;
        }
        
        $executionTime = microtime(true) - $startTime;
        
        return [
            'classification' => $classification,
            'confidence' => $confidence,
            'sub_scores' => $patterns,
            'reasoning' => 'Fallback analysis due to: ' . implode(', ', $errors),
            'processing_time' => $executionTime,
            'model_used' => 'php_fallback',
            'php_execution_time' => $executionTime,
            'timestamp' => date('c'),
            'fallback' => true,
            'errors' => $errors
        ];
    }
    
    /**
     * Analyze text patterns for AI indicators
     */
    private function analyzeTextPatterns($text) {
        $patterns = [
            'repetitive_phrases' => 0.0,
            'formal_language' => 0.0,
            'structured_format' => 0.0,
            'vocabulary_complexity' => 0.0,
            'sentence_uniformity' => 0.0
        ];
        
        $sentences = preg_split('/[.!?]+/', $text);
        $words = str_word_count($text, 1);
        
        if (count($sentences) > 1) {
            // Check sentence length uniformity
            $lengths = [];
            foreach ($sentences as $sentence) {
                $sentence = trim($sentence);
                if (!empty($sentence)) {
                    $lengths[] = str_word_count($sentence);
                }
            }
            
            if (!empty($lengths)) {
                $avgLength = array_sum($lengths) / count($lengths);
                $variance = 0;
                foreach ($lengths as $length) {
                    $variance += pow($length - $avgLength, 2);
                }
                $variance /= count($lengths);
                $patterns['sentence_uniformity'] = min(1.0, $variance / 100);
            }
        }
        
        // Check for formal language indicators
        $formalWords = ['furthermore', 'moreover', 'consequently', 'therefore', 'additionally', 'however', 'nevertheless'];
        $formalCount = 0;
        foreach ($words as $word) {
            if (in_array(strtolower($word), $formalWords)) {
                $formalCount++;
            }
        }
        $patterns['formal_language'] = min(1.0, $formalCount / max(1, count($words) / 50));
        
        // Check vocabulary complexity
        $uniqueWords = array_unique(array_map('strtolower', $words));
        if (count($words) > 0) {
            $patterns['vocabulary_complexity'] = count($uniqueWords) / count($words);
        }
        
        // Check for repetitive phrases
        $phrases = [];
        for ($i = 0; $i < count($words) - 2; $i++) {
            $phrase = strtolower($words[$i] . ' ' . $words[$i + 1] . ' ' . $words[$i + 2]);
            $phrases[] = $phrase;
        }
        $uniquePhrases = array_unique($phrases);
        if (count($phrases) > 0) {
            $patterns['repetitive_phrases'] = 1 - (count($uniquePhrases) / count($phrases));
        }
        
        // Check structured format (lists, numbered items, etc.)
        $structuredIndicators = preg_match_all('/^\s*[\d\-\*]\s+/m', $text);
        $patterns['structured_format'] = min(1.0, $structuredIndicators / max(1, count($sentences)));
        
        return $patterns;
    }
    
    /**
     * Log analysis events
     */
    private function logAnalysis($message, $data = []) {
        $logEntry = [
            'timestamp' => date('c'),
            'message' => $message,
            'data' => $data,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        $logLine = json_encode($logEntry) . "\n";
        file_put_contents($this->logPath, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get analysis statistics
     */
    public function getStats() {
        if (!file_exists($this->logPath)) {
            return ['total_analyses' => 0, 'success_rate' => 0];
        }
        
        $logs = file($this->logPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $total = 0;
        $successful = 0;
        
        foreach ($logs as $log) {
            $entry = json_decode($log, true);
            if ($entry && isset($entry['message'])) {
                if ($entry['message'] === 'Starting analysis') {
                    $total++;
                } elseif ($entry['message'] === 'Analysis completed') {
                    $successful++;
                }
            }
        }
        
        return [
            'total_analyses' => $total,
            'successful_analyses' => $successful,
            'success_rate' => $total > 0 ? ($successful / $total) * 100 : 0
        ];
    }
}

/**
 * Simple function for quick analysis
 */
function analyzeTextAI($text) {
    $service = new AIAnalysisService();
    return $service->analyzeText($text);
}
?>

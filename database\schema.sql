-- Simple AI Text Detector Database Schema
-- Basic schema for thesis project

CREATE DATABASE IF NOT EXISTS ai_text_detector CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ai_text_detector;

-- Basic users table for authentication
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Login attempts tracking for security
CREATE TABLE login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN DEFAULT FALSE,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_username_time (username, attempted_at),
    INDEX idx_ip_time (ip_address, attempted_at)
);

-- Analysis sessions
CREATE TABLE analysis_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    input_text LONGTEXT NOT NULL,
    input_type ENUM('text', 'file') DEFAULT 'text',
    file_name VARCHAR(255),
    file_size INT,
    file_type VARCHAR(50),
    analysis_result JSON,
    classification ENUM('AI-Generated', 'Human-Written', 'Mixed', 'Abstract') NOT NULL,
    confidence_score DECIMAL(5,2) NOT NULL,
    processing_time_ms INT,
    model_used VARCHAR(100),
    api_cost DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_classification (classification),
    INDEX idx_confidence (confidence_score),
    INDEX idx_created_at (created_at),
    INDEX idx_session_token (session_token)
);

-- Sub-scores for detailed analysis
CREATE TABLE analysis_subscores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    creativity_score DECIMAL(3,2),
    authenticity_score DECIMAL(3,2),
    linguistic_complexity_score DECIMAL(3,2),
    factual_alignment_score DECIMAL(3,2),
    emotional_expression_score DECIMAL(3,2),
    
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id)
);

-- User feedback system
CREATE TABLE feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    session_id INT,
    feedback_text TEXT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    feedback_type ENUM('general', 'accuracy', 'ui', 'feature_request', 'bug_report') DEFAULT 'general',
    is_resolved BOOLEAN DEFAULT FALSE,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_created_at (created_at)
);

-- API usage tracking
CREATE TABLE api_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_data JSON,
    response_status INT,
    response_time_ms INT,
    tokens_used INT,
    cost DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- System settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- File uploads tracking
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    session_id INT,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    is_processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_created_at (created_at)
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_name', 'AI Text Detector', 'string', 'Application name', TRUE),
('app_version', '2.0.0', 'string', 'Application version', TRUE),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode status', TRUE),
('max_daily_analyses', '100', 'integer', 'Maximum analyses per user per day', FALSE),
('supported_file_types', '["txt", "docx", "pdf"]', 'json', 'Supported file types for upload', TRUE),
('max_file_size', '10485760', 'integer', 'Maximum file size in bytes (10MB)', TRUE);

-- Create indexes for performance
CREATE INDEX idx_users_active ON users(is_active, created_at);
CREATE INDEX idx_analysis_user_date ON analysis_sessions(user_id, created_at);
CREATE INDEX idx_feedback_unresolved ON feedback(is_resolved, created_at);

-- Create views for common queries
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at as user_since,
    COUNT(a.id) as total_analyses,
    AVG(a.confidence_score) as avg_confidence,
    MAX(a.created_at) as last_analysis
FROM users u
LEFT JOIN analysis_sessions a ON u.id = a.user_id
GROUP BY u.id, u.username, u.email, u.created_at;

CREATE VIEW daily_usage_stats AS
SELECT 
    DATE(created_at) as analysis_date,
    COUNT(*) as total_analyses,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(confidence_score) as avg_confidence,
    AVG(processing_time_ms) as avg_processing_time
FROM analysis_sessions
GROUP BY DATE(created_at)
ORDER BY analysis_date DESC;

/**
 * Enhanced AI Detection System - Analytics Module
 * Advanced analytics and reporting functionality
 */

class Analytics {
    constructor(app) {
        this.app = app;
        this.charts = {};
        this.currentFilters = {
            timeRange: '7d',
            analysisType: 'all'
        };
        
        this.init();
    }
    
    init() {
        this.setupFilters();
        this.setupCharts();
        this.setupExportControls();
    }
    
    setupFilters() {
        const timeRange = document.getElementById('time-range');
        const analysisType = document.getElementById('analysis-type');
        const exportBtn = document.getElementById('export-analytics');
        
        if (timeRange) {
            timeRange.addEventListener('change', (e) => {
                this.currentFilters.timeRange = e.target.value;
                this.refreshAnalytics();
            });
        }
        
        if (analysisType) {
            analysisType.addEventListener('change', (e) => {
                this.currentFilters.analysisType = e.target.value;
                this.refreshAnalytics();
            });
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportAnalyticsReport());
        }
    }
    
    setupCharts() {
        this.initializeDistributionChart();
        this.initializeConfidenceChart();
        this.initializeDomainChart();
    }
    
    initializeDistributionChart() {
        const canvas = document.getElementById('distribution-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.distribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['AI-Generated', 'Human-Written', 'Professional', 'Academic'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#ef4444',
                        '#22c55e',
                        '#2563eb',
                        '#10b981'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    initializeConfidenceChart() {
        const canvas = document.getElementById('confidence-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.confidence = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Average Confidence',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'AI Detection Confidence',
                    data: [],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        display: true,
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }
    
    initializeDomainChart() {
        const canvas = document.getElementById('domain-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.domain = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Analyses by Domain',
                    data: [],
                    backgroundColor: [
                        '#2563eb',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444',
                        '#8b5cf6',
                        '#06b6d4'
                    ],
                    borderWidth: 1,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        display: true,
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
    
    async refreshAnalytics() {
        try {
            this.app.showLoading();
            
            const analyticsData = await this.app.apiCall('/api/analytics/dashboard', {
                timeRange: this.currentFilters.timeRange,
                analysisType: this.currentFilters.analysisType
            });
            
            this.updateCharts(analyticsData);
            this.updatePerformanceMetrics(analyticsData.performance);
            
        } catch (error) {
            console.error('Failed to refresh analytics:', error);
            this.app.showError('Failed to load analytics data');
        } finally {
            this.app.hideLoading();
        }
    }
    
    updateCharts(data) {
        if (data.distribution) {
            this.updateDistributionChart(data.distribution);
        }
        
        if (data.confidenceTrends) {
            this.updateConfidenceChart(data.confidenceTrends);
        }
        
        if (data.domainAnalysis) {
            this.updateDomainChart(data.domainAnalysis);
        }
    }
    
    updateDistributionChart(data) {
        if (!this.charts.distribution) return;
        
        const chart = this.charts.distribution;
        chart.data.datasets[0].data = [
            data.aiGenerated || 0,
            data.humanWritten || 0,
            data.professional || 0,
            data.academic || 0
        ];
        
        chart.update('active');
    }
    
    updateConfidenceChart(data) {
        if (!this.charts.confidence || !data.labels) return;
        
        const chart = this.charts.confidence;
        chart.data.labels = data.labels;
        chart.data.datasets[0].data = data.averageConfidence || [];
        chart.data.datasets[1].data = data.aiDetectionConfidence || [];
        
        chart.update('active');
    }
    
    updateDomainChart(data) {
        if (!this.charts.domain || !data.domains) return;
        
        const chart = this.charts.domain;
        chart.data.labels = data.domains.map(d => d.name);
        chart.data.datasets[0].data = data.domains.map(d => d.count);
        
        chart.update('active');
    }
    
    updatePerformanceMetrics(performance) {
        const metricsContainer = document.getElementById('performance-metrics');
        if (!metricsContainer || !performance) return;
        
        metricsContainer.innerHTML = `
            <div class="metric-item">
                <div class="metric-value">${Math.round((performance.accuracy || 0) * 100)}%</div>
                <div class="metric-label">Overall Accuracy</div>
                <div class="metric-change ${performance.accuracyChange >= 0 ? 'positive' : 'negative'}">
                    ${Math.abs(performance.accuracyChange || 0).toFixed(1)}%
                </div>
            </div>
            
            <div class="metric-item">
                <div class="metric-value">${Math.round((performance.precision || 0) * 100)}%</div>
                <div class="metric-label">Precision</div>
                <div class="metric-change ${performance.precisionChange >= 0 ? 'positive' : 'negative'}">
                    ${Math.abs(performance.precisionChange || 0).toFixed(1)}%
                </div>
            </div>
            
            <div class="metric-item">
                <div class="metric-value">${Math.round((performance.recall || 0) * 100)}%</div>
                <div class="metric-label">Recall</div>
                <div class="metric-change ${performance.recallChange >= 0 ? 'positive' : 'negative'}">
                    ${Math.abs(performance.recallChange || 0).toFixed(1)}%
                </div>
            </div>
            
            <div class="metric-item">
                <div class="metric-value">${Math.round((performance.f1Score || 0) * 100)}%</div>
                <div class="metric-label">F1 Score</div>
                <div class="metric-change ${performance.f1Change >= 0 ? 'positive' : 'negative'}">
                    ${Math.abs(performance.f1Change || 0).toFixed(1)}%
                </div>
            </div>
        `;
    }
    
    setupExportControls() {
        // Add additional export options
        this.addExportOptions();
    }
    
    addExportOptions() {
        const analyticsFilters = document.querySelector('.analytics-filters');
        if (!analyticsFilters || document.getElementById('export-options')) return;
        
        const exportOptions = document.createElement('div');
        exportOptions.id = 'export-options';
        exportOptions.className = 'export-options';
        exportOptions.innerHTML = `
            <select id="export-format">
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
                <option value="pdf">PDF Report</option>
            </select>
        `;
        
        const exportBtn = document.getElementById('export-analytics');
        if (exportBtn) {
            analyticsFilters.insertBefore(exportOptions, exportBtn);
        }
    }
    
    async exportAnalyticsReport() {
        const format = document.getElementById('export-format')?.value || 'json';
        
        try {
            this.app.showLoading();
            
            const reportData = await this.app.apiCall('/api/analytics/export', {
                timeRange: this.currentFilters.timeRange,
                analysisType: this.currentFilters.analysisType,
                format: format
            });
            
            this.downloadReport(reportData, format);
            this.app.showSuccess('Analytics report exported successfully');
            
        } catch (error) {
            console.error('Export failed:', error);
            this.app.showError('Failed to export analytics report');
        } finally {
            this.app.hideLoading();
        }
    }
    
    downloadReport(data, format) {
        let blob, filename;
        
        switch (format) {
            case 'csv':
                blob = new Blob([this.convertToCSV(data)], { type: 'text/csv' });
                filename = `analytics_report_${Date.now()}.csv`;
                break;
            case 'pdf':
                // For PDF, we would need a server-side service or PDF library
                this.app.showError('PDF export not yet implemented');
                return;
            default:
                blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                filename = `analytics_report_${Date.now()}.json`;
        }
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    convertToCSV(data) {
        if (!data.analyses || !Array.isArray(data.analyses)) {
            return 'No data available';
        }
        
        const headers = [
            'Date',
            'Classification',
            'Confidence',
            'Word Count',
            'Domain',
            'Processing Time'
        ];
        
        const rows = data.analyses.map(analysis => [
            new Date(analysis.createdAt).toLocaleDateString(),
            analysis.classification,
            Math.round(analysis.confidence * 100) + '%',
            analysis.wordCount,
            analysis.domain || 'N/A',
            analysis.processingTime + 'ms'
        ]);
        
        const csvContent = [
            headers.join(','),
            ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
        ].join('\n');
        
        return csvContent;
    }
    
    generateInsights(data) {
        const insights = [];
        
        if (data.distribution) {
            const total = Object.values(data.distribution).reduce((a, b) => a + b, 0);
            const aiPercentage = total > 0 ? (data.distribution.aiGenerated / total) * 100 : 0;
            
            if (aiPercentage > 50) {
                insights.push(`High AI content detected: ${aiPercentage.toFixed(1)}% of analyzed texts were AI-generated`);
            }
            
            if (data.distribution.professional > data.distribution.humanWritten * 0.5) {
                insights.push('Significant professional writing detected in your analyses');
            }
        }
        
        if (data.performance) {
            if (data.performance.accuracy > 0.9) {
                insights.push('Excellent detection accuracy maintained');
            } else if (data.performance.accuracy < 0.8) {
                insights.push('Detection accuracy could be improved');
            }
        }
        
        return insights;
    }
    
    displayInsights(insights) {
        const analyticsGrid = document.querySelector('.analytics-grid');
        if (!analyticsGrid || insights.length === 0) return;
        
        const insightsCard = document.createElement('div');
        insightsCard.className = 'analytics-card insights-card';
        insightsCard.innerHTML = `
            <h3>Key Insights</h3>
            <ul class="insights-list">
                ${insights.map(insight => `<li><i class="fas fa-lightbulb"></i> ${insight}</li>`).join('')}
            </ul>
        `;
        
        analyticsGrid.appendChild(insightsCard);
    }
    
    destroy() {
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.app) {
        window.analytics = new Analytics(window.app);
        
        // Add analytics reference to app
        window.app.analytics = window.analytics;
    }
});

/**
 * Enhanced AI Detection System - Batch Processing Module
 * Batch analysis functionality for multiple files
 */

class BatchProcessor {
    constructor(app) {
        this.app = app;
        this.queue = [];
        this.processing = false;
        this.maxConcurrent = 3;
        this.currentlyProcessing = 0;
        this.results = [];
        
        this.init();
    }
    
    init() {
        this.setupFileUpload();
        this.setupBatchOptions();
        this.setupQueueManagement();
    }
    
    setupFileUpload() {
        const batchFiles = document.getElementById('batch-files');
        
        if (batchFiles) {
            batchFiles.addEventListener('change', (e) => {
                this.handleBatchFiles(e.target.files);
            });
        }
    }
    
    setupBatchOptions() {
        const professionalOption = document.getElementById('batch-professional');
        const exportOption = document.getElementById('batch-export');
        
        this.batchOptions = {
            professionalAnalysis: professionalOption ? professionalOption.checked : true,
            exportResults: exportOption ? exportOption.checked : true
        };
        
        if (professionalOption) {
            professionalOption.addEventListener('change', (e) => {
                this.batchOptions.professionalAnalysis = e.target.checked;
            });
        }
        
        if (exportOption) {
            exportOption.addEventListener('change', (e) => {
                this.batchOptions.exportResults = e.target.checked;
            });
        }
    }
    
    setupQueueManagement() {
        // Add control buttons to batch section
        this.addBatchControls();
    }
    
    addBatchControls() {
        const batchContainer = document.querySelector('.batch-container');
        if (!batchContainer || document.getElementById('batch-controls')) return;
        
        const controlsDiv = document.createElement('div');
        controlsDiv.id = 'batch-controls';
        controlsDiv.className = 'batch-controls';
        controlsDiv.innerHTML = `
            <div class="controls-header">
                <h3>Batch Controls</h3>
                <div class="control-buttons">
                    <button class="btn btn-primary" id="start-batch" disabled>
                        <i class="fas fa-play"></i>
                        Start Processing
                    </button>
                    <button class="btn btn-secondary" id="pause-batch" disabled>
                        <i class="fas fa-pause"></i>
                        Pause
                    </button>
                    <button class="btn btn-danger" id="clear-queue">
                        <i class="fas fa-trash"></i>
                        Clear Queue
                    </button>
                </div>
            </div>
            <div class="batch-progress">
                <div class="progress-info">
                    <span id="progress-text">0 of 0 files processed</span>
                    <span id="progress-percentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
            </div>
        `;
        
        batchContainer.appendChild(controlsDiv);
        
        // Add event listeners
        document.getElementById('start-batch').addEventListener('click', () => this.startBatchProcessing());
        document.getElementById('pause-batch').addEventListener('click', () => this.pauseBatchProcessing());
        document.getElementById('clear-queue').addEventListener('click', () => this.clearQueue());
    }
    
    async handleBatchFiles(files) {
        const queueList = document.getElementById('queue-list');
        if (!queueList) return;
        
        for (const file of files) {
            if (this.isValidFileType(file)) {
                const queueItem = await this.createQueueItem(file);
                this.queue.push(queueItem);
                this.addQueueItemToDOM(queueItem);
            } else {
                this.app.showError(`Unsupported file type: ${file.name}`);
            }
        }
        
        this.updateBatchControls();
        this.updateProgress();
    }
    
    async createQueueItem(file) {
        const queueItem = {
            id: 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            file: file,
            fileName: file.name,
            fileSize: file.size,
            status: 'pending',
            progress: 0,
            result: null,
            error: null,
            startTime: null,
            endTime: null
        };
        
        // Extract text from file
        try {
            queueItem.text = await this.extractTextFromFile(file);
            queueItem.wordCount = queueItem.text.trim().split(/\s+/).length;
        } catch (error) {
            queueItem.status = 'error';
            queueItem.error = 'Failed to extract text from file';
        }
        
        return queueItem;
    }
    
    addQueueItemToDOM(queueItem) {
        const queueList = document.getElementById('queue-list');
        if (!queueList) return;
        
        const itemElement = document.createElement('div');
        itemElement.className = 'queue-item';
        itemElement.id = `queue-${queueItem.id}`;
        itemElement.innerHTML = `
            <div class="queue-info">
                <div class="file-icon">
                    <i class="fas fa-${this.getFileIcon(queueItem.file)}"></i>
                </div>
                <div class="file-details">
                    <h4>${queueItem.fileName}</h4>
                    <p>${this.formatFileSize(queueItem.fileSize)} • ${queueItem.wordCount || 0} words</p>
                </div>
            </div>
            <div class="queue-status">
                <div class="status-icon ${queueItem.status}"></div>
                <span class="status-text">${this.formatStatus(queueItem.status)}</span>
            </div>
            <div class="queue-actions">
                <button class="btn btn-sm btn-secondary" onclick="batchProcessor.removeFromQueue('${queueItem.id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        queueList.appendChild(itemElement);
    }
    
    async startBatchProcessing() {
        if (this.processing || this.queue.length === 0) return;
        
        this.processing = true;
        this.updateBatchControls();
        
        this.app.showSuccess(`Starting batch processing of ${this.queue.length} files`);
        
        // Process items with concurrency limit
        const pendingItems = this.queue.filter(item => item.status === 'pending');
        
        for (let i = 0; i < pendingItems.length; i += this.maxConcurrent) {
            if (!this.processing) break;
            
            const batch = pendingItems.slice(i, i + this.maxConcurrent);
            await Promise.all(batch.map(item => this.processQueueItem(item)));
        }
        
        this.processing = false;
        this.updateBatchControls();
        this.updateProgress();
        
        if (this.batchOptions.exportResults) {
            this.exportBatchResults();
        }
        
        this.app.showSuccess('Batch processing completed');
    }
    
    async processQueueItem(queueItem) {
        if (!this.processing || queueItem.status !== 'pending') return;
        
        try {
            queueItem.status = 'processing';
            queueItem.startTime = new Date();
            this.updateQueueItemDOM(queueItem);
            this.currentlyProcessing++;
            
            const analysisData = {
                text: queueItem.text,
                sessionId: this.app.sessionId,
                options: {
                    professionalAnalysis: this.batchOptions.professionalAnalysis,
                    domainDetection: true,
                    detailedBreakdown: false,
                    ensembleAnalysis: true
                },
                method: 'file',
                fileName: queueItem.fileName
            };
            
            const result = await this.app.apiCall('/api/analyze', analysisData, 'POST');
            
            queueItem.result = result;
            queueItem.status = 'completed';
            queueItem.endTime = new Date();
            this.results.push({
                queueItem,
                result
            });
            
        } catch (error) {
            queueItem.status = 'failed';
            queueItem.error = error.message;
            queueItem.endTime = new Date();
            console.error(`Failed to process ${queueItem.fileName}:`, error);
        } finally {
            this.currentlyProcessing--;
            this.updateQueueItemDOM(queueItem);
            this.updateProgress();
        }
    }
    
    updateQueueItemDOM(queueItem) {
        const itemElement = document.getElementById(`queue-${queueItem.id}`);
        if (!itemElement) return;
        
        const statusIcon = itemElement.querySelector('.status-icon');
        const statusText = itemElement.querySelector('.status-text');
        
        if (statusIcon) {
            statusIcon.className = `status-icon ${queueItem.status}`;
        }
        
        if (statusText) {
            statusText.textContent = this.formatStatus(queueItem.status);
        }
        
        itemElement.className = `queue-item ${queueItem.status}`;
        
        // Add result preview for completed items
        if (queueItem.status === 'completed' && queueItem.result) {
            const existingResult = itemElement.querySelector('.result-preview');
            if (!existingResult) {
                const resultPreview = document.createElement('div');
                resultPreview.className = 'result-preview';
                resultPreview.innerHTML = `
                    <div class="result-badge ${this.app.getClassificationClass(queueItem.result.classification)}">
                        ${queueItem.result.classification}
                    </div>
                    <span class="confidence">${Math.round(queueItem.result.confidence * 100)}%</span>
                `;
                itemElement.querySelector('.queue-info').appendChild(resultPreview);
            }
        }
    }
    
    pauseBatchProcessing() {
        this.processing = false;
        this.updateBatchControls();
        this.app.showSuccess('Batch processing paused');
    }
    
    clearQueue() {
        if (this.processing) {
            this.pauseBatchProcessing();
        }
        
        this.queue = [];
        this.results = [];
        this.currentlyProcessing = 0;
        
        const queueList = document.getElementById('queue-list');
        if (queueList) {
            queueList.innerHTML = '';
        }
        
        this.updateBatchControls();
        this.updateProgress();
        
        this.app.showSuccess('Queue cleared');
    }
    
    removeFromQueue(itemId) {
        const index = this.queue.findIndex(item => item.id === itemId);
        if (index !== -1) {
            this.queue.splice(index, 1);
            
            const itemElement = document.getElementById(`queue-${itemId}`);
            if (itemElement) {
                itemElement.remove();
            }
            
            this.updateBatchControls();
            this.updateProgress();
        }
    }
    
    updateBatchControls() {
        const startBtn = document.getElementById('start-batch');
        const pauseBtn = document.getElementById('pause-batch');
        
        if (startBtn) {
            startBtn.disabled = this.processing || this.queue.length === 0;
        }
        
        if (pauseBtn) {
            pauseBtn.disabled = !this.processing;
        }
    }
    
    updateProgress() {
        const totalItems = this.queue.length;
        const completedItems = this.queue.filter(item => 
            item.status === 'completed' || item.status === 'failed'
        ).length;
        
        const progressText = document.getElementById('progress-text');
        const progressPercentage = document.getElementById('progress-percentage');
        const progressFill = document.getElementById('progress-fill');
        
        const percentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
        
        if (progressText) {
            progressText.textContent = `${completedItems} of ${totalItems} files processed`;
        }
        
        if (progressPercentage) {
            progressPercentage.textContent = `${percentage}%`;
        }
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
    }
    
    exportBatchResults() {
        if (this.results.length === 0) {
            this.app.showError('No results to export');
            return;
        }
        
        const exportData = {
            timestamp: new Date().toISOString(),
            totalFiles: this.queue.length,
            successfulAnalyses: this.results.length,
            batchOptions: this.batchOptions,
            results: this.results.map(({ queueItem, result }) => ({
                fileName: queueItem.fileName,
                fileSize: queueItem.fileSize,
                wordCount: queueItem.wordCount,
                processingTime: queueItem.endTime - queueItem.startTime,
                classification: result.classification,
                confidence: result.confidence,
                professionalAnalysis: result.professionalAnalysis,
                reasoning: result.reasoning
            }))
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `batch_analysis_results_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.app.showSuccess('Batch results exported successfully');
    }
    
    // Utility methods
    async extractTextFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                    resolve(e.target.result);
                } else {
                    // For other file types, simulate extraction
                    resolve(`[Extracted text from ${file.name}]\n\nThis is simulated text extraction for batch processing.`);
                }
            };
            
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }
    
    isValidFileType(file) {
        const validTypes = [
            'text/plain',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        return validTypes.includes(file.type) || file.name.endsWith('.txt');
    }
    
    getFileIcon(file) {
        if (file.type.includes('pdf')) return 'file-pdf';
        if (file.type.includes('word')) return 'file-word';
        return 'file-text';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatStatus(status) {
        const statusMap = {
            'pending': 'Pending',
            'processing': 'Processing...',
            'completed': 'Completed',
            'failed': 'Failed',
            'error': 'Error'
        };
        return statusMap[status] || status;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.app) {
        window.batchProcessor = new BatchProcessor(window.app);
    }
});

<?php
/**
 * Enhanced Database Migration Script
 * Creates all tables for the enhanced AI detection system
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../services/database_service.php';

class EnhancedDatabaseMigration {
    private $pdo;
    
    public function __construct() {
        $config = EnhancedConfig::get('database');
        
        $dsn = sprintf(
            "mysql:host=%s;port=%d;charset=%s",
            $config['host'],
            $config['port'],
            $config['charset']
        );
        
        $this->pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    }
    
    public function migrate() {
        try {
            echo "Starting enhanced database migration...\n";
            
            // Create database if it doesn't exist
            $this->createDatabase();
            
            // Use the database
            $config = EnhancedConfig::get('database');
            $this->pdo->exec("USE `{$config['name']}`");
            
            // Create tables in order of dependencies
            $this->createUsersTable();
            $this->createAnalysisSessionsTable();
            $this->createAnalysisResultsTable();
            $this->createSubScoresTable();
            $this->createProfessionalAnalysisTable();
            $this->createMLModelPerformanceTable();
            $this->createUserFeedbackTable();
            $this->createTrainingDataTable();
            $this->createApiUsageLogsTable();
            $this->createRateLimitsTable();
            $this->createSystemAnalyticsTable();
            
            // Create views
            $this->createViews();
            
            // Insert sample data
            $this->insertSampleData();
            
            echo "Enhanced database migration completed successfully!\n";
            
        } catch (Exception $e) {
            echo "Migration failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    private function createDatabase() {
        $config = EnhancedConfig::get('database');
        $sql = "CREATE DATABASE IF NOT EXISTS `{$config['name']}` 
                CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $this->pdo->exec($sql);
        echo "Database created/verified: {$config['name']}\n";
    }
    
    private function createUsersTable() {
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50),
            last_name VARCHAR(50),
            role ENUM('admin', 'researcher', 'user') DEFAULT 'user',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            preferences JSON,
            last_login TIMESTAMP NULL,
            login_attempts INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: users\n";
    }
    
    private function createAnalysisSessionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS analysis_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(64) UNIQUE NOT NULL,
            user_id INT,
            text_content LONGTEXT NOT NULL,
            text_hash VARCHAR(64) NOT NULL,
            word_count INT NOT NULL,
            character_count INT NOT NULL,
            file_name VARCHAR(255),
            file_type VARCHAR(50),
            file_size INT,
            status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            processing_time_ms INT,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_session_id (session_id),
            INDEX idx_user_id (user_id),
            INDEX idx_text_hash (text_hash),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: analysis_sessions\n";
    }
    
    private function createAnalysisResultsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS analysis_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(64) NOT NULL,
            model_name VARCHAR(100) NOT NULL,
            classification ENUM('AI-Generated', 'Human-Written', 'Professional-Human', 'Academic-Human', 'Uncertain') NOT NULL,
            confidence DECIMAL(5,4) NOT NULL,
            reasoning TEXT,
            raw_response LONGTEXT,
            is_primary_result BOOLEAN DEFAULT FALSE,
            processing_time_ms INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES analysis_sessions(session_id) ON DELETE CASCADE,
            INDEX idx_session_id (session_id),
            INDEX idx_model_name (model_name),
            INDEX idx_classification (classification),
            INDEX idx_confidence (confidence),
            INDEX idx_is_primary (is_primary_result)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: analysis_results\n";
    }
    
    private function createSubScoresTable() {
        $sql = "CREATE TABLE IF NOT EXISTS sub_scores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            result_id INT NOT NULL,
            score_type VARCHAR(50) NOT NULL,
            score_value DECIMAL(8,4) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (result_id) REFERENCES analysis_results(id) ON DELETE CASCADE,
            INDEX idx_result_id (result_id),
            INDEX idx_score_type (score_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: sub_scores\n";
    }
    
    private function createProfessionalAnalysisTable() {
        $sql = "CREATE TABLE IF NOT EXISTS professional_analysis (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(64) NOT NULL,
            domain_detected VARCHAR(100),
            expertise_level ENUM('novice', 'intermediate', 'advanced', 'expert') DEFAULT 'novice',
            terminology_score DECIMAL(5,4) DEFAULT 0,
            citation_count INT DEFAULT 0,
            structure_score DECIMAL(5,4) DEFAULT 0,
            formality_score DECIMAL(5,4) DEFAULT 0,
            technical_depth_score DECIMAL(5,4) DEFAULT 0,
            domain_keywords JSON,
            detected_patterns JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES analysis_sessions(session_id) ON DELETE CASCADE,
            INDEX idx_session_id (session_id),
            INDEX idx_domain (domain_detected),
            INDEX idx_expertise_level (expertise_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: professional_analysis\n";
    }
    
    private function createMLModelPerformanceTable() {
        $sql = "CREATE TABLE IF NOT EXISTS ml_model_performance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            model_name VARCHAR(100) NOT NULL,
            model_version VARCHAR(50) NOT NULL,
            accuracy DECIMAL(6,5) NOT NULL,
            precision_score DECIMAL(6,5) NOT NULL,
            recall DECIMAL(6,5) NOT NULL,
            f1_score DECIMAL(6,5) NOT NULL,
            training_date DATE NOT NULL,
            test_samples INT NOT NULL,
            confusion_matrix JSON,
            feature_importance JSON,
            hyperparameters JSON,
            evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_model_name (model_name),
            INDEX idx_model_version (model_version),
            INDEX idx_evaluation_date (evaluation_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: ml_model_performance\n";
    }
    
    private function createUserFeedbackTable() {
        $sql = "CREATE TABLE IF NOT EXISTS user_feedback (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(64) NOT NULL,
            user_id INT,
            feedback_type ENUM('correction', 'rating', 'comment') NOT NULL,
            original_classification VARCHAR(50) NOT NULL,
            suggested_classification VARCHAR(50),
            confidence_rating INT CHECK (confidence_rating BETWEEN 1 AND 5),
            feedback_text TEXT,
            is_verified BOOLEAN DEFAULT FALSE,
            verified_by INT,
            verified_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES analysis_sessions(session_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_session_id (session_id),
            INDEX idx_user_id (user_id),
            INDEX idx_feedback_type (feedback_type),
            INDEX idx_is_verified (is_verified)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: user_feedback\n";
    }
    
    private function createTrainingDataTable() {
        $sql = "CREATE TABLE IF NOT EXISTS training_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            text_content LONGTEXT NOT NULL,
            text_hash VARCHAR(64) UNIQUE NOT NULL,
            true_label ENUM('AI-Generated', 'Human-Written', 'Professional-Human', 'Academic-Human') NOT NULL,
            source VARCHAR(100) NOT NULL,
            domain VARCHAR(100),
            quality_score DECIMAL(3,2) DEFAULT 1.0,
            verified BOOLEAN DEFAULT FALSE,
            verified_by INT,
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_text_hash (text_hash),
            INDEX idx_true_label (true_label),
            INDEX idx_source (source),
            INDEX idx_domain (domain),
            INDEX idx_verified (verified)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: training_data\n";
    }
    
    private function createApiUsageLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS api_usage_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            endpoint VARCHAR(255) NOT NULL,
            method VARCHAR(10) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            request_size INT,
            response_size INT,
            response_time_ms INT,
            status_code INT NOT NULL,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_endpoint (endpoint),
            INDEX idx_ip_address (ip_address),
            INDEX idx_status_code (status_code),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: api_usage_logs\n";
    }
    
    private function createRateLimitsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS rate_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            identifier_type ENUM('ip', 'user', 'api_key') NOT NULL,
            requests_count INT NOT NULL DEFAULT 0,
            window_start TIMESTAMP NOT NULL,
            window_end TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_identifier (identifier, identifier_type),
            INDEX idx_identifier (identifier),
            INDEX idx_window_end (window_end)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: rate_limits\n";
    }
    
    private function createSystemAnalyticsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS system_analytics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            metric_name VARCHAR(100) NOT NULL,
            metric_value DECIMAL(15,6) NOT NULL,
            metric_type ENUM('gauge', 'counter', 'histogram') DEFAULT 'gauge',
            tags JSON,
            recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_metric_name (metric_name),
            INDEX idx_recorded_at (recorded_at),
            INDEX idx_metric_type (metric_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
        echo "Created table: system_analytics\n";
    }

    private function createViews() {
        // Analysis summary view
        $sql = "CREATE OR REPLACE VIEW analysis_summary AS
                SELECT
                    s.session_id,
                    s.user_id,
                    s.word_count,
                    s.character_count,
                    s.status,
                    s.processing_time_ms,
                    ar.classification,
                    ar.confidence,
                    ar.model_name,
                    pa.domain_detected,
                    pa.expertise_level,
                    s.created_at
                FROM analysis_sessions s
                LEFT JOIN analysis_results ar ON s.session_id = ar.session_id AND ar.is_primary_result = TRUE
                LEFT JOIN professional_analysis pa ON s.session_id = pa.session_id";

        $this->pdo->exec($sql);
        echo "Created view: analysis_summary\n";

        // User analytics view
        $sql = "CREATE OR REPLACE VIEW user_analytics AS
                SELECT
                    u.id as user_id,
                    u.username,
                    u.role,
                    COUNT(s.id) as total_analyses,
                    AVG(ar.confidence) as avg_confidence,
                    COUNT(CASE WHEN ar.classification = 'AI-Generated' THEN 1 END) as ai_detected,
                    COUNT(CASE WHEN ar.classification = 'Human-Written' THEN 1 END) as human_detected,
                    COUNT(CASE WHEN ar.classification = 'Professional-Human' THEN 1 END) as professional_detected,
                    MAX(s.created_at) as last_analysis,
                    AVG(s.processing_time_ms) as avg_processing_time
                FROM users u
                LEFT JOIN analysis_sessions s ON u.id = s.user_id
                LEFT JOIN analysis_results ar ON s.session_id = ar.session_id AND ar.is_primary_result = TRUE
                GROUP BY u.id, u.username, u.role";

        $this->pdo->exec($sql);
        echo "Created view: user_analytics\n";

        // Model performance view
        $sql = "CREATE OR REPLACE VIEW model_performance_summary AS
                SELECT
                    model_name,
                    COUNT(*) as total_evaluations,
                    AVG(accuracy) as avg_accuracy,
                    AVG(precision_score) as avg_precision,
                    AVG(recall) as avg_recall,
                    AVG(f1_score) as avg_f1_score,
                    MAX(evaluation_date) as latest_evaluation
                FROM ml_model_performance
                GROUP BY model_name";

        $this->pdo->exec($sql);
        echo "Created view: model_performance_summary\n";
    }

    private function insertSampleData() {
        // Insert admin user
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, preferences)
                VALUES ('admin', '<EMAIL>', ?, 'System', 'Administrator', 'admin',
                        JSON_OBJECT('theme', 'dark', 'notifications', true, 'auto_save', true))";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$adminPassword]);
        echo "Inserted admin user\n";

        // Insert researcher user
        $researcherPassword = password_hash('researcher123', PASSWORD_DEFAULT);
        $sql = "INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, preferences)
                VALUES ('researcher', '<EMAIL>', ?, 'AI', 'Researcher', 'researcher',
                        JSON_OBJECT('theme', 'light', 'notifications', true, 'detailed_analysis', true))";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$researcherPassword]);
        echo "Inserted researcher user\n";

        // Insert sample training data
        $sampleTexts = [
            [
                'content' => 'The implementation of artificial intelligence in modern healthcare systems has revolutionized patient care delivery. Through sophisticated machine learning algorithms, medical professionals can now analyze vast datasets to identify patterns that were previously undetectable. This technological advancement has led to more accurate diagnoses, personalized treatment plans, and improved patient outcomes across various medical specialties.',
                'label' => 'AI-Generated',
                'source' => 'GPT-4',
                'domain' => 'medical'
            ],
            [
                'content' => 'I have been working as a cardiologist for over 15 years, and I can tell you that the integration of AI tools in our practice has been both exciting and challenging. While these systems can process ECGs faster than any human, I still rely heavily on my clinical experience when making complex diagnostic decisions. The human touch in medicine cannot be replaced by algorithms, no matter how sophisticated they become.',
                'label' => 'Professional-Human',
                'source' => 'Medical Professional',
                'domain' => 'medical'
            ],
            [
                'content' => 'According to recent studies published in the Journal of Machine Learning Research, ensemble methods have demonstrated superior performance in text classification tasks compared to single-model approaches. The methodology employed in this research involved training multiple neural networks with different architectures and combining their predictions through weighted voting mechanisms. Statistical significance was established using paired t-tests with p < 0.05.',
                'label' => 'Academic-Human',
                'source' => 'Academic Paper',
                'domain' => 'academic'
            ]
        ];

        foreach ($sampleTexts as $text) {
            $sql = "INSERT IGNORE INTO training_data (text_content, text_hash, true_label, source, domain, quality_score, verified)
                    VALUES (?, ?, ?, ?, ?, 1.0, TRUE)";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $text['content'],
                hash('sha256', $text['content']),
                $text['label'],
                $text['source'],
                $text['domain']
            ]);
        }
        echo "Inserted sample training data\n";

        // Insert sample model performance data
        $sql = "INSERT IGNORE INTO ml_model_performance
                (model_name, model_version, accuracy, precision_score, recall, f1_score, training_date, test_samples,
                 confusion_matrix, feature_importance, hyperparameters)
                VALUES
                ('GPT-4-Detector', '1.0', 0.92, 0.89, 0.94, 0.91, CURDATE(), 1000,
                 JSON_OBJECT('tp', 450, 'fp', 50, 'tn', 470, 'fn', 30),
                 JSON_OBJECT('perplexity', 0.35, 'burstiness', 0.28, 'semantic_coherence', 0.22),
                 JSON_OBJECT('learning_rate', 0.001, 'batch_size', 32, 'epochs', 10)),
                ('Ensemble-Model', '2.1', 0.95, 0.93, 0.96, 0.94, CURDATE(), 1500,
                 JSON_OBJECT('tp', 720, 'fp', 30, 'tn', 720, 'fn', 30),
                 JSON_OBJECT('gpt4_weight', 0.4, 'bert_weight', 0.3, 'custom_weight', 0.3),
                 JSON_OBJECT('voting_strategy', 'weighted', 'confidence_threshold', 0.7))";

        $this->pdo->exec($sql);
        echo "Inserted sample model performance data\n";
    }

    public function rollback() {
        try {
            echo "Rolling back enhanced database migration...\n";

            $tables = [
                'system_analytics',
                'rate_limits',
                'api_usage_logs',
                'training_data',
                'user_feedback',
                'ml_model_performance',
                'professional_analysis',
                'sub_scores',
                'analysis_results',
                'analysis_sessions',
                'users'
            ];

            foreach ($tables as $table) {
                $this->pdo->exec("DROP TABLE IF EXISTS $table");
                echo "Dropped table: $table\n";
            }

            // Drop views
            $views = ['analysis_summary', 'user_analytics', 'model_performance_summary'];
            foreach ($views as $view) {
                $this->pdo->exec("DROP VIEW IF EXISTS $view");
                echo "Dropped view: $view\n";
            }

            echo "Database rollback completed successfully!\n";

        } catch (Exception $e) {
            echo "Rollback failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $migration = new EnhancedDatabaseMigration();

    $command = $argv[1] ?? 'migrate';

    switch ($command) {
        case 'migrate':
            $migration->migrate();
            break;
        case 'rollback':
            $migration->rollback();
            break;
        default:
            echo "Usage: php create_enhanced_tables.php [migrate|rollback]\n";
            exit(1);
    }
} else {
    echo "This script must be run from the command line.\n";
    exit(1);
}
?>

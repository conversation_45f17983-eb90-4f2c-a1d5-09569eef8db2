Stack trace:
Frame         Function      Args
0007FFFF97F0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF86F0) msys-2.0.dll+0x1FEBA
0007FFFF97F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9AC8) msys-2.0.dll+0x67F9
0007FFFF97F0  000210046832 (000210285FF9, 0007FFFF96A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF97F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF97F0  0002100690B4 (0007FFFF9800, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9AD0  00021006A49D (0007FFFF9800, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCB25E0000 ntdll.dll
7FFC8B4C0000 aswhook.dll
7FFCB1B10000 KERNEL32.DLL
7FFCAF730000 KERNELBASE.dll
7FFCB0960000 USER32.dll
7FFCB00A0000 win32u.dll
000210040000 msys-2.0.dll
7FFCB1CF0000 GDI32.dll
7FFCAFF60000 gdi32full.dll
7FFCAFD60000 msvcp_win.dll
7FFCAFE10000 ucrtbase.dll
7FFCB0E60000 advapi32.dll
7FFCB1DA0000 msvcrt.dll
7FFCB1BF0000 sechost.dll
7FFCB1970000 RPCRT4.dll
7FFCAED30000 CRYPTBASE.DLL
7FFCB02E0000 bcryptPrimitives.dll
7FFCB1AC0000 IMM32.DLL

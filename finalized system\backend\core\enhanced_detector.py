#!/usr/bin/env python3
"""
Enhanced AI Text Detection Engine
Advanced multi-model ensemble approach with professional writing analysis
"""

import os
import sys
import json
import time
import hashlib
import logging
import asyncio
import aiohttp
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import numpy as np
import re
from openai import OpenAI
import spacy
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler
import joblib

@dataclass
class AnalysisResult:
    """Enhanced analysis result with comprehensive metrics"""
    session_id: str
    classification: str
    confidence: float
    sub_scores: Dict[str, float]
    reasoning: str
    professional_analysis: Dict[str, Any]
    ensemble_results: List[Dict[str, Any]]
    processing_time: float
    model_used: str
    timestamp: str
    text_hash: str
    word_count: int
    character_count: int

class ProfessionalWritingAnalyzer:
    """Specialized analyzer for professional and academic writing"""
    
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.domain_keywords = {
            'academic': ['research', 'study', 'analysis', 'methodology', 'hypothesis', 'conclusion', 'literature', 'peer-reviewed'],
            'legal': ['pursuant', 'whereas', 'heretofore', 'plaintiff', 'defendant', 'jurisdiction', 'statute'],
            'medical': ['diagnosis', 'treatment', 'patient', 'symptoms', 'clinical', 'therapeutic', 'pathology'],
            'technical': ['implementation', 'algorithm', 'optimization', 'framework', 'architecture', 'specification'],
            'business': ['stakeholder', 'revenue', 'strategy', 'market', 'competitive', 'ROI', 'KPI']
        }
        
    def analyze(self, text: str) -> Dict[str, Any]:
        """Comprehensive professional writing analysis"""
        doc = self.nlp(text)
        
        # Domain detection
        domain = self._detect_domain(text)
        
        # Expertise level assessment
        expertise_level = self._assess_expertise_level(text, doc)
        
        # Terminology analysis
        terminology_score = self._analyze_terminology(text, domain)
        
        # Citation and reference analysis
        citation_count = self._count_citations(text)
        
        # Structure analysis
        structure_score = self._analyze_structure(text, doc)
        
        # Formality assessment
        formality_score = self._assess_formality(doc)
        
        # Technical depth
        technical_depth = self._assess_technical_depth(text, doc)
        
        return {
            'domain_detected': domain,
            'expertise_level': expertise_level,
            'terminology_score': terminology_score,
            'citation_count': citation_count,
            'structure_score': structure_score,
            'formality_score': formality_score,
            'technical_depth_score': technical_depth,
            'domain_keywords_found': self._find_domain_keywords(text, domain),
            'sentence_complexity': self._analyze_sentence_complexity(doc),
            'vocabulary_sophistication': self._assess_vocabulary_sophistication(doc)
        }
    
    def _detect_domain(self, text: str) -> str:
        """Detect the professional domain of the text"""
        text_lower = text.lower()
        domain_scores = {}
        
        for domain, keywords in self.domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            domain_scores[domain] = score
        
        if not domain_scores or max(domain_scores.values()) == 0:
            return 'general'
        
        return max(domain_scores, key=domain_scores.get)
    
    def _assess_expertise_level(self, text: str, doc) -> str:
        """Assess the expertise level of the writing"""
        # Factors: vocabulary complexity, sentence structure, domain knowledge
        vocab_score = len(set(token.text.lower() for token in doc if token.is_alpha)) / len([token for token in doc if token.is_alpha])
        avg_sentence_length = np.mean([len(sent) for sent in doc.sents])
        
        if vocab_score > 0.7 and avg_sentence_length > 25:
            return 'expert'
        elif vocab_score > 0.5 and avg_sentence_length > 20:
            return 'professional'
        elif vocab_score > 0.3 and avg_sentence_length > 15:
            return 'intermediate'
        else:
            return 'novice'
    
    def _analyze_terminology(self, text: str, domain: str) -> float:
        """Analyze domain-specific terminology usage"""
        if domain == 'general':
            return 3.0
        
        keywords = self.domain_keywords.get(domain, [])
        found_keywords = sum(1 for keyword in keywords if keyword in text.lower())
        
        return min(5.0, (found_keywords / len(keywords)) * 5.0)
    
    def _count_citations(self, text: str) -> int:
        """Count academic citations and references"""
        citation_patterns = [
            r'\([A-Za-z]+,?\s+\d{4}\)',  # (Author, 2023)
            r'\[\d+\]',                   # [1]
            r'et al\.',                   # et al.
            r'ibid\.',                    # ibid.
            r'op\. cit\.',               # op. cit.
        ]
        
        count = 0
        for pattern in citation_patterns:
            count += len(re.findall(pattern, text))
        
        return count
    
    def _analyze_structure(self, text: str, doc) -> float:
        """Analyze text structure and organization"""
        sentences = list(doc.sents)
        if len(sentences) < 3:
            return 2.0
        
        # Check for logical flow indicators
        transition_words = ['however', 'therefore', 'furthermore', 'moreover', 'consequently', 'nevertheless']
        transitions_found = sum(1 for word in transition_words if word in text.lower())
        
        # Check for paragraph structure (assuming double newlines)
        paragraphs = text.split('\n\n')
        
        structure_score = 0
        if len(paragraphs) > 1:
            structure_score += 1
        if transitions_found > 0:
            structure_score += min(2, transitions_found)
        if len(sentences) > 5:
            structure_score += 1
        
        return min(5.0, structure_score)
    
    def _assess_formality(self, doc) -> float:
        """Assess the formality level of the text"""
        formal_indicators = 0
        total_tokens = len([token for token in doc if token.is_alpha])
        
        # Check for contractions (informal)
        contractions = sum(1 for token in doc if "'" in token.text)
        
        # Check for passive voice (formal)
        passive_voice = sum(1 for token in doc if token.dep_ == "auxpass")
        
        # Check for first person pronouns (informal in academic writing)
        first_person = sum(1 for token in doc if token.text.lower() in ['i', 'me', 'my', 'we', 'us', 'our'])
        
        formality_score = 3.0  # baseline
        formality_score += (passive_voice / total_tokens) * 10
        formality_score -= (contractions / total_tokens) * 10
        formality_score -= (first_person / total_tokens) * 5
        
        return max(1.0, min(5.0, formality_score))
    
    def _assess_technical_depth(self, text: str, doc) -> float:
        """Assess technical depth and complexity"""
        # Look for technical indicators
        technical_patterns = [
            r'\b[A-Z]{2,}\b',  # Acronyms
            r'\d+\.\d+',       # Decimal numbers
            r'[a-zA-Z]+\([^)]*\)',  # Function-like patterns
        ]
        
        technical_score = 0
        for pattern in technical_patterns:
            matches = len(re.findall(pattern, text))
            technical_score += min(1, matches * 0.1)
        
        # Check for complex sentence structures
        complex_sentences = sum(1 for sent in doc.sents if len(sent) > 30)
        technical_score += min(2, complex_sentences * 0.2)
        
        return min(5.0, technical_score)
    
    def _find_domain_keywords(self, text: str, domain: str) -> List[str]:
        """Find domain-specific keywords in the text"""
        if domain == 'general':
            return []
        
        keywords = self.domain_keywords.get(domain, [])
        found = [keyword for keyword in keywords if keyword in text.lower()]
        return found
    
    def _analyze_sentence_complexity(self, doc) -> float:
        """Analyze sentence complexity"""
        sentences = list(doc.sents)
        if not sentences:
            return 0.0
        
        avg_length = np.mean([len(sent) for sent in sentences])
        complexity_score = min(5.0, avg_length / 10)
        return complexity_score
    
    def _assess_vocabulary_sophistication(self, doc) -> float:
        """Assess vocabulary sophistication"""
        words = [token.text.lower() for token in doc if token.is_alpha and len(token.text) > 3]
        if not words:
            return 0.0
        
        # Simple sophistication metric based on word length and uniqueness
        avg_word_length = np.mean([len(word) for word in words])
        unique_ratio = len(set(words)) / len(words)
        
        sophistication = (avg_word_length / 10) + unique_ratio
        return min(5.0, sophistication * 2.5)

class EnsembleDetector:
    """Ensemble detector combining multiple AI models and ML approaches"""
    
    def __init__(self):
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.professional_analyzer = ProfessionalWritingAnalyzer()
        self.ml_models = {}
        self.vectorizer = TfidfVectorizer(max_features=5000, stop_words='english')
        self.scaler = StandardScaler()
        self.setup_logging()
        self.load_ml_models()
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def load_ml_models(self):
        """Load pre-trained ML models"""
        models_path = 'ml_pipeline/models/'
        try:
            if os.path.exists(f'{models_path}random_forest.joblib'):
                self.ml_models['random_forest'] = joblib.load(f'{models_path}random_forest.joblib')
            if os.path.exists(f'{models_path}svm.joblib'):
                self.ml_models['svm'] = joblib.load(f'{models_path}svm.joblib')
            if os.path.exists(f'{models_path}neural_network.joblib'):
                self.ml_models['neural_network'] = joblib.load(f'{models_path}neural_network.joblib')
            if os.path.exists(f'{models_path}vectorizer.joblib'):
                self.vectorizer = joblib.load(f'{models_path}vectorizer.joblib')
            if os.path.exists(f'{models_path}scaler.joblib'):
                self.scaler = joblib.load(f'{models_path}scaler.joblib')
        except Exception as e:
            self.logger.warning(f"Could not load ML models: {e}")
    
    async def analyze_text(self, text: str, session_id: str = None) -> AnalysisResult:
        """Comprehensive text analysis using ensemble approach"""
        start_time = time.time()
        
        if not session_id:
            session_id = f"session_{int(time.time())}_{hash(text) % 10000}"
        
        # Generate text hash and metrics
        text_hash = hashlib.sha256(text.encode()).hexdigest()
        word_count = len(text.split())
        character_count = len(text)
        
        # Professional writing analysis
        professional_analysis = self.professional_analyzer.analyze(text)
        
        # Ensemble analysis
        ensemble_results = await self._run_ensemble_analysis(text)
        
        # Combine results
        final_result = self._combine_ensemble_results(ensemble_results, professional_analysis)
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        return AnalysisResult(
            session_id=session_id,
            classification=final_result['classification'],
            confidence=final_result['confidence'],
            sub_scores=final_result['sub_scores'],
            reasoning=final_result['reasoning'],
            professional_analysis=professional_analysis,
            ensemble_results=ensemble_results,
            processing_time=processing_time,
            model_used="Enhanced Ensemble v2.0",
            timestamp=datetime.now().isoformat(),
            text_hash=text_hash,
            word_count=word_count,
            character_count=character_count
        )
    
    async def _run_ensemble_analysis(self, text: str) -> List[Dict[str, Any]]:
        """Run analysis using multiple models"""
        results = []
        
        # OpenAI GPT-4 analysis
        try:
            gpt4_result = await self._analyze_with_gpt4(text)
            results.append(gpt4_result)
        except Exception as e:
            self.logger.error(f"GPT-4 analysis failed: {e}")
        
        # OpenAI GPT-4-mini analysis
        try:
            gpt4_mini_result = await self._analyze_with_gpt4_mini(text)
            results.append(gpt4_mini_result)
        except Exception as e:
            self.logger.error(f"GPT-4-mini analysis failed: {e}")
        
        # ML models analysis
        if self.ml_models:
            try:
                ml_result = self._analyze_with_ml_models(text)
                results.append(ml_result)
            except Exception as e:
                self.logger.error(f"ML models analysis failed: {e}")
        
        return results

    async def _analyze_with_gpt4(self, text: str) -> Dict[str, Any]:
        """Analyze text using GPT-4 with enhanced prompting"""
        enhanced_prompt = """You are an advanced AI text detection system with expertise in identifying AI-generated content, especially in professional and academic contexts.

Analyze the provided text considering these factors:
1. **Professional Writing Patterns**: Look for authentic domain expertise, genuine professional experience, and real-world knowledge
2. **Human Authenticity Markers**: Personal insights, cultural context, emotional nuance, and experiential knowledge
3. **AI Generation Indicators**: Overly formal language, generic statements, lack of specific details, repetitive patterns
4. **Academic/Professional Context**: Proper use of terminology, citation patterns, field-specific knowledge

For professional/academic texts, consider:
- Genuine expertise vs. surface-level knowledge
- Authentic research experience vs. generic academic language
- Real-world application vs. theoretical generalities
- Personal voice vs. artificial formality

Provide your analysis in this exact format:
**Classification**: [AI-Generated/Human-Written/Professional-Human/Academic-Human/Mixed] (Confidence: X%)

**Sub-Scores**:
- Creativity: X/5 (originality and unique perspectives)
- Authenticity: X/5 (genuine human experience and voice)
- Language: X/5 (natural flow and complexity)
- Expertise: X/5 (domain knowledge and professional insight)
- Emotional Expression: X/5 (genuine emotional content)

**Reasoning**: [Detailed explanation focusing on professional writing characteristics]"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": enhanced_prompt},
                    {"role": "user", "content": f"Analyze this text:\n\n{text}"}
                ],
                max_tokens=2000,
                temperature=0.2
            )

            ai_response = response.choices[0].message.content.strip()
            return self._parse_openai_response(ai_response, "gpt-4o")

        except Exception as e:
            self.logger.error(f"GPT-4 analysis error: {e}")
            return {
                'model': 'gpt-4o',
                'classification': 'Error',
                'confidence': 0.0,
                'sub_scores': {},
                'reasoning': f"Analysis failed: {str(e)}"
            }

    async def _analyze_with_gpt4_mini(self, text: str) -> Dict[str, Any]:
        """Analyze text using GPT-4-mini for comparison"""
        prompt = """Analyze this text to determine if it's AI-generated or human-written. Focus on:
1. Language naturalness and flow
2. Emotional authenticity
3. Professional expertise indicators
4. Cultural and contextual knowledge

Respond with:
Classification: [AI-Generated/Human-Written/Mixed] (Confidence: X%)
Creativity: X/5
Authenticity: X/5
Language: X/5
Expertise: X/5
Reasoning: [Brief explanation]"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": text}
                ],
                max_tokens=1500,
                temperature=0.3
            )

            ai_response = response.choices[0].message.content.strip()
            return self._parse_openai_response(ai_response, "gpt-4o-mini")

        except Exception as e:
            self.logger.error(f"GPT-4-mini analysis error: {e}")
            return {
                'model': 'gpt-4o-mini',
                'classification': 'Error',
                'confidence': 0.0,
                'sub_scores': {},
                'reasoning': f"Analysis failed: {str(e)}"
            }

    def _analyze_with_ml_models(self, text: str) -> Dict[str, Any]:
        """Analyze text using trained ML models"""
        try:
            # Extract features
            features = self._extract_features(text)

            # Get predictions from all available models
            predictions = {}
            confidences = {}

            for model_name, model in self.ml_models.items():
                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba([features])[0]
                    pred = model.predict([features])[0]
                    predictions[model_name] = pred
                    confidences[model_name] = max(proba)
                else:
                    pred = model.predict([features])[0]
                    predictions[model_name] = pred
                    confidences[model_name] = 0.8  # Default confidence

            # Ensemble the ML predictions
            if predictions:
                # Simple majority voting
                ai_votes = sum(1 for pred in predictions.values() if pred == 1)
                human_votes = sum(1 for pred in predictions.values() if pred == 0)

                if ai_votes > human_votes:
                    classification = "AI-Generated"
                    confidence = np.mean([conf for conf in confidences.values()]) * 100
                else:
                    classification = "Human-Written"
                    confidence = np.mean([conf for conf in confidences.values()]) * 100

                return {
                    'model': 'ML-Ensemble',
                    'classification': classification,
                    'confidence': confidence,
                    'sub_scores': {
                        'ML_Confidence': confidence / 20,  # Scale to 5
                        'Model_Agreement': (max(ai_votes, human_votes) / len(predictions)) * 5
                    },
                    'reasoning': f"ML ensemble prediction based on {len(predictions)} models. Votes: AI={ai_votes}, Human={human_votes}",
                    'individual_predictions': predictions,
                    'individual_confidences': confidences
                }
            else:
                return {
                    'model': 'ML-Ensemble',
                    'classification': 'Error',
                    'confidence': 0.0,
                    'sub_scores': {},
                    'reasoning': "No ML models available for prediction"
                }

        except Exception as e:
            self.logger.error(f"ML analysis error: {e}")
            return {
                'model': 'ML-Ensemble',
                'classification': 'Error',
                'confidence': 0.0,
                'sub_scores': {},
                'reasoning': f"ML analysis failed: {str(e)}"
            }

    def _extract_features(self, text: str) -> np.ndarray:
        """Extract features for ML models"""
        # Basic linguistic features
        word_count = len(text.split())
        char_count = len(text)
        avg_word_length = np.mean([len(word) for word in text.split()])
        sentence_count = len([s for s in text.split('.') if s.strip()])
        avg_sentence_length = word_count / max(sentence_count, 1)

        # Punctuation features
        punct_count = sum(1 for char in text if char in '.,!?;:')
        punct_ratio = punct_count / max(char_count, 1)

        # Complexity features
        unique_words = len(set(text.lower().split()))
        lexical_diversity = unique_words / max(word_count, 1)

        # Professional writing indicators
        formal_words = ['however', 'therefore', 'furthermore', 'moreover', 'consequently']
        formal_count = sum(1 for word in formal_words if word in text.lower())

        # Combine features
        features = np.array([
            word_count, char_count, avg_word_length, avg_sentence_length,
            punct_ratio, lexical_diversity, formal_count
        ])

        # Scale features if scaler is available
        if hasattr(self.scaler, 'transform'):
            features = self.scaler.transform([features])[0]

        return features

    def _parse_openai_response(self, response: str, model_name: str) -> Dict[str, Any]:
        """Parse OpenAI response to extract structured data"""
        result = {
            'model': model_name,
            'classification': 'Mixed',
            'confidence': 50.0,
            'sub_scores': {},
            'reasoning': response
        }

        # Extract classification and confidence
        classification_pattern = r'Classification[:\s]*([^,\n(]+).*?Confidence[:\s]*([\d.]+)%'
        match = re.search(classification_pattern, response, re.IGNORECASE | re.DOTALL)
        if match:
            classification = match.group(1).strip()
            confidence = float(match.group(2))

            # Normalize classification
            if 'ai-generated' in classification.lower() or 'ai generated' in classification.lower():
                result['classification'] = 'AI-Generated'
            elif 'human-written' in classification.lower() or 'human written' in classification.lower():
                result['classification'] = 'Human-Written'
            elif 'professional-human' in classification.lower():
                result['classification'] = 'Professional-Human'
            elif 'academic-human' in classification.lower():
                result['classification'] = 'Academic-Human'
            elif 'mixed' in classification.lower():
                result['classification'] = 'Mixed'

            result['confidence'] = confidence

        # Extract sub-scores
        score_patterns = [
            ('Creativity', r'Creativity[:\s]*([\d.]+)/5'),
            ('Authenticity', r'Authenticity[:\s]*([\d.]+)/5'),
            ('Language', r'Language[:\s]*([\d.]+)/5'),
            ('Expertise', r'Expertise[:\s]*([\d.]+)/5'),
            ('Emotional Expression', r'Emotional Expression[:\s]*([\d.]+)/5')
        ]

        for score_name, pattern in score_patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                result['sub_scores'][score_name] = float(match.group(1))

        return result

    def _combine_ensemble_results(self, ensemble_results: List[Dict[str, Any]],
                                professional_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Combine results from multiple models using weighted voting"""
        if not ensemble_results:
            return {
                'classification': 'Error',
                'confidence': 0.0,
                'sub_scores': {},
                'reasoning': 'No analysis results available'
            }

        # Model weights (can be adjusted based on performance)
        model_weights = {
            'gpt-4o': 0.5,
            'gpt-4o-mini': 0.3,
            'ML-Ensemble': 0.2
        }

        # Collect classifications and confidences
        classifications = []
        confidences = []
        all_sub_scores = {}
        reasoning_parts = []

        for result in ensemble_results:
            if result['classification'] != 'Error':
                model_name = result['model']
                weight = model_weights.get(model_name, 0.1)

                classifications.append((result['classification'], weight, result['confidence']))
                confidences.append(result['confidence'] * weight)

                # Collect sub-scores
                for score_name, score_value in result.get('sub_scores', {}).items():
                    if score_name not in all_sub_scores:
                        all_sub_scores[score_name] = []
                    all_sub_scores[score_name].append(score_value * weight)

                reasoning_parts.append(f"{model_name}: {result['reasoning'][:200]}...")

        # Determine final classification using weighted voting
        classification_votes = {}
        total_weight = 0

        for classification, weight, confidence in classifications:
            adjusted_weight = weight * (confidence / 100)  # Adjust weight by confidence
            if classification not in classification_votes:
                classification_votes[classification] = 0
            classification_votes[classification] += adjusted_weight
            total_weight += adjusted_weight

        # Get the classification with highest weighted vote
        if classification_votes:
            final_classification = max(classification_votes, key=classification_votes.get)
            final_confidence = (classification_votes[final_classification] / total_weight) * 100
        else:
            final_classification = 'Mixed'
            final_confidence = 50.0

        # Enhance classification based on professional analysis
        if professional_analysis:
            expertise_level = professional_analysis.get('expertise_level', 'novice')
            domain = professional_analysis.get('domain_detected', 'general')

            # Adjust classification for professional content
            if expertise_level in ['expert', 'professional'] and domain != 'general':
                if final_classification == 'Human-Written':
                    if domain in ['academic', 'research']:
                        final_classification = 'Academic-Human'
                    else:
                        final_classification = 'Professional-Human'
                    # Boost confidence for professional content
                    final_confidence = min(95.0, final_confidence * 1.1)

        # Average sub-scores
        final_sub_scores = {}
        for score_name, scores in all_sub_scores.items():
            if scores:
                final_sub_scores[score_name] = sum(scores) / len(scores)

        # Add professional analysis scores
        if professional_analysis:
            final_sub_scores.update({
                'Professional_Terminology': professional_analysis.get('terminology_score', 0),
                'Structure_Quality': professional_analysis.get('structure_score', 0),
                'Formality_Level': professional_analysis.get('formality_score', 0),
                'Technical_Depth': professional_analysis.get('technical_depth_score', 0)
            })

        # Generate comprehensive reasoning
        reasoning = f"Enhanced ensemble analysis combining multiple AI models and professional writing analysis.\n\n"
        reasoning += f"Final Classification: {final_classification} (Confidence: {final_confidence:.1f}%)\n\n"

        if professional_analysis:
            reasoning += f"Professional Analysis:\n"
            reasoning += f"- Domain: {professional_analysis.get('domain_detected', 'general')}\n"
            reasoning += f"- Expertise Level: {professional_analysis.get('expertise_level', 'novice')}\n"
            reasoning += f"- Citations Found: {professional_analysis.get('citation_count', 0)}\n\n"

        reasoning += "Model Contributions:\n" + "\n".join(reasoning_parts)

        return {
            'classification': final_classification,
            'confidence': final_confidence,
            'sub_scores': final_sub_scores,
            'reasoning': reasoning
        }

# Legacy compatibility function
def is_generated_by_ai(text: str, model: str = "enhanced-ensemble") -> Tuple[str, float, Dict[str, float], str]:
    """Legacy compatibility function for the enhanced detector"""
    import asyncio

    detector = EnsembleDetector()

    # Run async analysis in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(detector.analyze_text(text))
        return result.classification, result.confidence, result.sub_scores, result.reasoning
    finally:
        loop.close()

# Command line interface
if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        input_text = ' '.join(sys.argv[1:])

        # Create and run detector
        detector = EnsembleDetector()

        # Run analysis
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(detector.analyze_text(input_text))

            # Output results
            print(f"Classification: {result.classification}")
            print(f"Confidence: {result.confidence:.2f}%")
            print(f"Processing Time: {result.processing_time:.2f}ms")
            print(f"\nSub-Scores:")
            for score_name, score_value in result.sub_scores.items():
                print(f"  {score_name}: {score_value:.2f}")
            print(f"\nReasoning:\n{result.reasoning}")

            if result.professional_analysis:
                print(f"\nProfessional Analysis:")
                prof = result.professional_analysis
                print(f"  Domain: {prof.get('domain_detected', 'N/A')}")
                print(f"  Expertise Level: {prof.get('expertise_level', 'N/A')}")
                print(f"  Citations: {prof.get('citation_count', 0)}")

        finally:
            loop.close()
    else:
        print("Usage: python enhanced_detector.py <text_to_analyze>")
        print("Example: python enhanced_detector.py 'This is a sample text to analyze.'")

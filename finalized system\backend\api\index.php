<?php
/**
 * Enhanced AI Detection System - API Endpoint
 * Main API router for frontend requests
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../services/database_service.php';
require_once '../core/enhanced_detector.py';

class APIRouter {
    private $databaseService;
    private $config;
    
    public function __construct() {
        $this->config = require '../config/config.php';
        $this->databaseService = new DatabaseService();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/finalized system/backend/api', '', $path);
        
        // Get session ID from headers
        $sessionId = $_SERVER['HTTP_X_SESSION_ID'] ?? null;
        
        try {
            switch ($path) {
                case '/analyze':
                    if ($method === 'POST') {
                        $this->handleAnalyze($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                case '/user/stats':
                    if ($method === 'GET') {
                        $this->handleUserStats($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                case '/analyses/recent':
                    if ($method === 'GET') {
                        $this->handleRecentAnalyses($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                case '/analytics/trends':
                    if ($method === 'GET') {
                        $this->handleAnalyticsTrends($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                case '/analytics/dashboard':
                    if ($method === 'GET') {
                        $this->handleAnalyticsDashboard($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                case '/analytics/export':
                    if ($method === 'GET') {
                        $this->handleAnalyticsExport($sessionId);
                    } else {
                        $this->sendError('Method not allowed', 405);
                    }
                    break;
                    
                default:
                    $this->sendError('Endpoint not found', 404);
            }
        } catch (Exception $e) {
            error_log('API Error: ' . $e->getMessage());
            $this->sendError('Internal server error', 500);
        }
    }
    
    private function handleAnalyze($sessionId) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['text'])) {
            $this->sendError('Text is required', 400);
            return;
        }
        
        $text = $input['text'];
        $options = $input['options'] ?? [];
        $method = $input['method'] ?? 'text';
        $fileName = $input['fileName'] ?? null;
        
        // Validate text length
        if (strlen($text) < 50) {
            $this->sendError('Text must be at least 50 characters long', 400);
            return;
        }
        
        // Create analysis session
        $sessionData = [
            'session_id' => $sessionId,
            'user_id' => 1, // Default user for demo
            'text_content' => $text,
            'text_hash' => hash('sha256', $text),
            'word_count' => str_word_count($text),
            'character_count' => strlen($text),
            'file_name' => $fileName,
            'file_type' => $method,
            'status' => 'processing'
        ];
        
        $analysisId = $this->databaseService->createAnalysisSession($sessionData);
        
        // Simulate AI detection analysis
        $result = $this->simulateAnalysis($text, $options);
        
        // Store results
        $resultData = [
            'analysis_id' => $analysisId,
            'classification' => $result['classification'],
            'confidence_score' => $result['confidence'],
            'ai_probability' => $result['scores']['aiProbability'] ?? 0.5,
            'human_probability' => $result['scores']['humanProbability'] ?? 0.5,
            'professional_score' => $result['scores']['professionalScore'] ?? 0.0,
            'reasoning' => json_encode($result['reasoning']),
            'model_scores' => json_encode($result['modelScores'] ?? []),
            'processing_time' => rand(1000, 5000) // Simulated processing time
        ];
        
        $this->databaseService->storeAnalysisResult($resultData);
        
        // Store professional analysis if enabled
        if ($options['professionalAnalysis'] ?? true) {
            $professionalData = [
                'analysis_id' => $analysisId,
                'domain' => $result['professionalAnalysis']['domain'] ?? 'General',
                'expertise_level' => $result['professionalAnalysis']['expertiseLevel'] ?? 'Intermediate',
                'credibility_score' => $result['professionalAnalysis']['credibilityScore'] ?? 0.7,
                'authenticity_indicators' => json_encode($result['professionalAnalysis']['authenticityIndicators'] ?? []),
                'domain_keywords' => json_encode($result['professionalAnalysis']['domainKeywords'] ?? [])
            ];
            
            $this->databaseService->storeProfessionalAnalysis($professionalData);
        }
        
        $this->sendSuccess($result);
    }
    
    private function handleUserStats($sessionId) {
        $stats = $this->databaseService->getUserStats(1); // Default user
        $this->sendSuccess($stats);
    }
    
    private function handleRecentAnalyses($sessionId) {
        $limit = $_GET['limit'] ?? 10;
        $analyses = $this->databaseService->getRecentAnalyses(1, $limit);
        $this->sendSuccess($analyses);
    }
    
    private function handleAnalyticsTrends($sessionId) {
        $period = $_GET['period'] ?? '7d';
        $trends = $this->databaseService->getAnalyticsTrends(1, $period);
        $this->sendSuccess($trends);
    }
    
    private function handleAnalyticsDashboard($sessionId) {
        $timeRange = $_GET['timeRange'] ?? '7d';
        $analysisType = $_GET['analysisType'] ?? 'all';
        
        $analytics = [
            'distribution' => $this->databaseService->getClassificationDistribution(1, $timeRange),
            'confidenceTrends' => $this->databaseService->getConfidenceTrends(1, $timeRange),
            'domainAnalysis' => $this->databaseService->getDomainAnalysis(1, $timeRange),
            'performance' => $this->databaseService->getPerformanceMetrics(1, $timeRange)
        ];
        
        $this->sendSuccess($analytics);
    }
    
    private function handleAnalyticsExport($sessionId) {
        $timeRange = $_GET['timeRange'] ?? '7d';
        $analysisType = $_GET['analysisType'] ?? 'all';
        $format = $_GET['format'] ?? 'json';
        
        $exportData = [
            'timestamp' => date('c'),
            'timeRange' => $timeRange,
            'analysisType' => $analysisType,
            'analyses' => $this->databaseService->getAnalysesForExport(1, $timeRange, $analysisType),
            'summary' => $this->databaseService->getAnalyticsSummary(1, $timeRange)
        ];
        
        $this->sendSuccess($exportData);
    }
    
    private function simulateAnalysis($text, $options) {
        // Simulate AI detection analysis
        $wordCount = str_word_count($text);
        $hasAcademicTerms = $this->hasAcademicTerms($text);
        $hasProfessionalTerms = $this->hasProfessionalTerms($text);
        $complexity = $this->calculateComplexity($text);
        
        // Simulate classification logic
        $aiProbability = 0.3 + (rand(0, 40) / 100); // 30-70%
        $humanProbability = 1 - $aiProbability;
        
        $classification = 'Human-Written';
        if ($aiProbability > 0.6) {
            $classification = 'AI-Generated';
        } elseif ($hasProfessionalTerms && $complexity > 0.7) {
            $classification = 'Professional-Human';
        } elseif ($hasAcademicTerms && $complexity > 0.8) {
            $classification = 'Academic-Human';
        }
        
        $confidence = 0.7 + (rand(0, 25) / 100); // 70-95%
        
        $reasoning = [];
        if ($classification === 'AI-Generated') {
            $reasoning[] = 'Repetitive sentence structures detected';
            $reasoning[] = 'Lack of personal voice or opinion';
            $reasoning[] = 'Overly formal language patterns';
        } else {
            $reasoning[] = 'Natural language flow and variation';
            $reasoning[] = 'Personal writing style indicators';
            if ($hasProfessionalTerms) {
                $reasoning[] = 'Professional domain expertise evident';
            }
        }
        
        $result = [
            'classification' => $classification,
            'confidence' => $confidence,
            'scores' => [
                'aiProbability' => $aiProbability,
                'humanProbability' => $humanProbability,
                'professionalScore' => $hasProfessionalTerms ? 0.8 : 0.3,
                'complexityScore' => $complexity,
                'authenticityScore' => $humanProbability * 0.9
            ],
            'reasoning' => $reasoning,
            'modelScores' => [
                'gpt4' => $confidence * 0.95,
                'gpt4mini' => $confidence * 0.85,
                'ensemble' => $confidence
            ]
        ];
        
        if ($options['professionalAnalysis'] ?? true) {
            $result['professionalAnalysis'] = [
                'domain' => $this->detectDomain($text),
                'expertiseLevel' => $this->detectExpertiseLevel($text),
                'credibilityScore' => 0.7 + (rand(0, 25) / 100),
                'authenticityIndicators' => [
                    'personalExperience' => $humanProbability > 0.6,
                    'domainKnowledge' => $hasProfessionalTerms,
                    'originalThinking' => $complexity > 0.6
                ],
                'domainKeywords' => $this->extractDomainKeywords($text)
            ];
        }
        
        return $result;
    }
    
    private function hasAcademicTerms($text) {
        $academicTerms = ['research', 'study', 'analysis', 'methodology', 'hypothesis', 'conclusion', 'literature', 'peer-reviewed'];
        $text = strtolower($text);
        foreach ($academicTerms as $term) {
            if (strpos($text, $term) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function hasProfessionalTerms($text) {
        $professionalTerms = ['strategy', 'implementation', 'optimization', 'framework', 'methodology', 'best practices', 'stakeholders'];
        $text = strtolower($text);
        foreach ($professionalTerms as $term) {
            if (strpos($text, $term) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function calculateComplexity($text) {
        $sentences = preg_split('/[.!?]+/', $text);
        $avgWordsPerSentence = str_word_count($text) / max(count($sentences), 1);
        $longWords = preg_match_all('/\b\w{7,}\b/', $text);
        
        return min(($avgWordsPerSentence / 20) + ($longWords / str_word_count($text)), 1.0);
    }
    
    private function detectDomain($text) {
        $domains = [
            'Academic' => ['research', 'study', 'analysis', 'methodology'],
            'Legal' => ['contract', 'legal', 'court', 'law'],
            'Medical' => ['patient', 'treatment', 'diagnosis', 'medical'],
            'Technical' => ['system', 'algorithm', 'implementation', 'technical'],
            'Business' => ['strategy', 'market', 'business', 'revenue']
        ];
        
        $text = strtolower($text);
        $scores = [];
        
        foreach ($domains as $domain => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                $score += substr_count($text, $keyword);
            }
            $scores[$domain] = $score;
        }
        
        return array_keys($scores, max($scores))[0] ?? 'General';
    }
    
    private function detectExpertiseLevel($text) {
        $complexity = $this->calculateComplexity($text);
        if ($complexity > 0.8) return 'Expert';
        if ($complexity > 0.6) return 'Advanced';
        if ($complexity > 0.4) return 'Intermediate';
        return 'Novice';
    }
    
    private function extractDomainKeywords($text) {
        // Simple keyword extraction
        $words = str_word_count($text, 1);
        $keywords = array_filter($words, function($word) {
            return strlen($word) > 6;
        });
        return array_slice(array_unique($keywords), 0, 10);
    }
    
    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ]);
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ]);
    }
}

// Initialize and handle request
$router = new APIRouter();
$router->handleRequest();
?>

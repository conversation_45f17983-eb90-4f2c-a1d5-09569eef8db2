/**
 * Enhanced AI Detection System - Dashboard Module
 * Dashboard functionality and data visualization
 */

class Dashboard {
    constructor(app) {
        this.app = app;
        this.charts = {};
        this.refreshInterval = null;
        this.autoRefresh = true;
        
        this.init();
    }
    
    init() {
        this.setupCharts();
        this.setupRefreshControls();
        this.startAutoRefresh();
    }
    
    setupCharts() {
        // Initialize Chart.js charts
        this.initializeAccuracyChart();
    }
    
    initializeAccuracyChart() {
        const canvas = document.getElementById('accuracy-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.accuracy = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Detection Accuracy',
                    data: [],
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgb(37, 99, 235)',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#64748b'
                        }
                    },
                    y: {
                        display: true,
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: '#e2e8f0'
                        },
                        ticks: {
                            color: '#64748b',
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }
    
    updateAccuracyChart(data) {
        if (!this.charts.accuracy || !data) return;
        
        const chart = this.charts.accuracy;
        
        // Update chart data
        chart.data.labels = data.labels || [];
        chart.data.datasets[0].data = data.values || [];
        
        // Animate the update
        chart.update('active');
    }
    
    setupRefreshControls() {
        // Add refresh button to dashboard header if it doesn't exist
        const dashboardHeader = document.querySelector('#dashboard .section-header');
        if (dashboardHeader && !document.getElementById('refresh-dashboard')) {
            const refreshButton = document.createElement('button');
            refreshButton.id = 'refresh-dashboard';
            refreshButton.className = 'btn btn-secondary btn-sm';
            refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
            refreshButton.addEventListener('click', () => this.refreshDashboard());
            
            dashboardHeader.appendChild(refreshButton);
        }
    }
    
    async refreshDashboard() {
        try {
            const refreshBtn = document.getElementById('refresh-dashboard');
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;
            }
            
            await this.app.loadDashboardData();
            
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
                refreshBtn.disabled = false;
            }
            
        } catch (error) {
            console.error('Dashboard refresh failed:', error);
            this.app.showError('Failed to refresh dashboard');
        }
    }
    
    startAutoRefresh() {
        if (this.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                // Only refresh if dashboard is visible
                if (this.app.currentSection === 'dashboard') {
                    this.refreshDashboard();
                }
            }, 30000); // Refresh every 30 seconds
        }
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    updateStats(stats) {
        // Animate stat updates
        const statElements = {
            'total-analyses': stats.totalAnalyses || 0,
            'ai-detected': stats.aiDetected || 0,
            'professional-detected': stats.professionalDetected || 0,
            'avg-confidence': Math.round((stats.avgConfidence || 0) * 100)
        };
        
        Object.entries(statElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateStatValue(element, value, id === 'avg-confidence');
            }
        });
    }
    
    animateStatValue(element, targetValue, isPercentage = false) {
        const currentValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 30;
        let current = currentValue;
        
        const animation = setInterval(() => {
            current += increment;
            
            if ((increment > 0 && current >= targetValue) || 
                (increment < 0 && current <= targetValue)) {
                current = targetValue;
                clearInterval(animation);
            }
            
            element.textContent = Math.round(current) + (isPercentage ? '%' : '');
        }, 50);
    }
    
    updateRecentAnalyses(analyses) {
        const container = document.getElementById('recent-analyses');
        if (!container) return;
        
        if (!analyses || analyses.length === 0) {
            this.showEmptyState(container);
            return;
        }
        
        // Clear existing content
        container.innerHTML = '';
        
        // Add each analysis with staggered animation
        analyses.forEach((analysis, index) => {
            setTimeout(() => {
                const analysisElement = this.createAnalysisElement(analysis);
                container.appendChild(analysisElement);
                
                // Trigger fade-in animation
                requestAnimationFrame(() => {
                    analysisElement.classList.add('fade-in');
                });
            }, index * 100);
        });
    }
    
    createAnalysisElement(analysis) {
        const element = document.createElement('div');
        element.className = 'analysis-item';
        element.innerHTML = `
            <div class="analysis-info">
                <h4>${this.truncateText(analysis.textPreview || 'Text Analysis', 50)}</h4>
                <div class="analysis-meta">
                    <span><i class="fas fa-clock"></i> ${this.formatRelativeTime(analysis.createdAt)}</span>
                    <span><i class="fas fa-file-text"></i> ${analysis.wordCount || 0} words</span>
                    ${analysis.domain ? `<span><i class="fas fa-tag"></i> ${analysis.domain}</span>` : ''}
                </div>
            </div>
            <div class="analysis-result">
                <div class="result-badge ${this.getClassificationClass(analysis.classification)}">
                    ${analysis.classification || 'Unknown'}
                </div>
                <div class="confidence-indicator">
                    ${Math.round((analysis.confidence || 0) * 100)}%
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${(analysis.confidence || 0) * 100}%"></div>
                    </div>
                </div>
            </div>
        `;
        
        // Add click handler for viewing details
        element.addEventListener('click', () => {
            this.showAnalysisDetails(analysis);
        });
        
        return element;
    }
    
    showEmptyState(container) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-chart-line"></i>
                <h3>No analyses yet</h3>
                <p>Start by analyzing some text to see your results here.</p>
                <button class="btn btn-primary" onclick="app.showSection('analyze')">
                    <i class="fas fa-plus"></i>
                    Start Analysis
                </button>
            </div>
        `;
    }
    
    showAnalysisDetails(analysis) {
        // Create modal or detailed view for analysis
        const modal = this.createAnalysisModal(analysis);
        document.body.appendChild(modal);
        
        // Show modal with animation
        requestAnimationFrame(() => {
            modal.classList.add('active');
        });
    }
    
    createAnalysisModal(analysis) {
        const modal = document.createElement('div');
        modal.className = 'analysis-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Analysis Details</h3>
                    <button class="modal-close" onclick="this.closest('.analysis-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="analysis-summary">
                        <div class="summary-item">
                            <label>Classification:</label>
                            <span class="result-badge ${this.getClassificationClass(analysis.classification)}">
                                ${analysis.classification}
                            </span>
                        </div>
                        <div class="summary-item">
                            <label>Confidence:</label>
                            <span>${Math.round((analysis.confidence || 0) * 100)}%</span>
                        </div>
                        <div class="summary-item">
                            <label>Word Count:</label>
                            <span>${analysis.wordCount || 0}</span>
                        </div>
                        <div class="summary-item">
                            <label>Analysis Date:</label>
                            <span>${this.formatDate(analysis.createdAt)}</span>
                        </div>
                    </div>
                    
                    ${analysis.textPreview ? `
                        <div class="text-preview">
                            <h4>Text Preview</h4>
                            <div class="preview-content">${analysis.textPreview}</div>
                        </div>
                    ` : ''}
                    
                    ${analysis.reasoning ? `
                        <div class="analysis-reasoning">
                            <h4>Analysis Reasoning</h4>
                            <ul>
                                ${analysis.reasoning.map(reason => `<li>${reason}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.analysis-modal').remove()">
                        Close
                    </button>
                    <button class="btn btn-primary" onclick="this.exportAnalysis('${analysis.id}')">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    // Utility methods
    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    formatRelativeTime(dateString) {
        if (!dateString) return 'Unknown';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return date.toLocaleDateString();
    }
    
    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        return new Date(dateString).toLocaleString();
    }
    
    getClassificationClass(classification) {
        const classMap = {
            'AI-Generated': 'ai',
            'Human-Written': 'human',
            'Professional-Human': 'professional',
            'Academic-Human': 'academic',
            'Uncertain': 'uncertain'
        };
        return classMap[classification] || 'uncertain';
    }
    
    exportAnalysis(analysisId) {
        // Implementation for exporting specific analysis
        this.app.showSuccess('Export functionality will be implemented');
    }
    
    destroy() {
        this.stopAutoRefresh();
        
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.app) {
        window.dashboard = new Dashboard(window.app);
        
        // Add dashboard reference to app
        window.app.dashboard = window.dashboard;
    }
});

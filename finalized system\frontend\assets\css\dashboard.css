/* Enhanced AI Detection System - Dashboard Styles */

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card:nth-child(2)::before {
    background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.stat-card:nth-child(3)::before {
    background: linear-gradient(90deg, var(--accent-color), #34d399);
}

.stat-card:nth-child(4)::before {
    background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    background: var(--primary-color);
    flex-shrink: 0;
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--danger-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--accent-color);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--warning-color);
}

.stat-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-1) 0;
    line-height: 1;
}

.stat-content p {
    margin: 0;
    color: var(--gray-600);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-8);
}

.dashboard-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.dashboard-card .card-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-6);
}

.dashboard-card .card-content {
    padding: 0;
}

/* Recent Analyses */
.recent-analyses {
    max-height: 400px;
    overflow-y: auto;
}

.analysis-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--gray-100);
    transition: background var(--transition-fast);
}

.analysis-item:hover {
    background: var(--gray-50);
}

.analysis-item:last-child {
    border-bottom: none;
}

.analysis-info {
    flex: 1;
}

.analysis-info h4 {
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    font-weight: 600;
}

.analysis-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.analysis-result {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.result-badge {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-badge.ai {
    background: var(--danger-color);
    color: var(--white);
}

.result-badge.human {
    background: var(--success-color);
    color: var(--white);
}

.result-badge.professional {
    background: var(--primary-color);
    color: var(--white);
}

.result-badge.academic {
    background: var(--accent-color);
    color: var(--white);
}

.confidence-indicator {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
}

.confidence-bar {
    width: 60px;
    height: 4px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-top: var(--spacing-1);
}

.confidence-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-slow);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-8);
    color: var(--gray-500);
}

.empty-state i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

.empty-state h3 {
    margin-bottom: var(--spacing-2);
    color: var(--gray-600);
}

.empty-state p {
    margin-bottom: var(--spacing-4);
}

/* Analytics Section */
.analytics-dashboard {
    margin-top: var(--spacing-6);
}

.analytics-filters {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.analytics-filters select {
    width: auto;
    min-width: 150px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
}

.analytics-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.analytics-card h3 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
    font-size: var(--font-size-lg);
}

.analytics-card canvas {
    max-height: 300px;
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
}

.metric-item {
    text-align: center;
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-1);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
}

.metric-change {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-1);
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--danger-color);
}

.metric-change::before {
    content: "↗";
    margin-right: var(--spacing-1);
}

.metric-change.negative::before {
    content: "↘";
}

/* Settings Section */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
}

.settings-group {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.settings-group h3 {
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--gray-200);
}

.setting-item {
    margin-bottom: var(--spacing-5);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--gray-700);
}

.setting-item input[type="range"] {
    width: 100%;
    margin-bottom: var(--spacing-2);
}

.setting-item input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-2);
}

#threshold-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }
    
    .analytics-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .analytics-filters select {
        width: 100%;
    }
    
    .performance-metrics {
        grid-template-columns: 1fr;
    }
    
    .analysis-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }
    
    .analysis-result {
        align-self: flex-end;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--spacing-2);
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

.skeleton-card {
    padding: var(--spacing-4);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-3);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

<?php
/**
 * MySQL Fix Tool for XAMPP
 * Fixes common MySQL startup issues
 */

echo "🔧 MySQL Fix Tool\n";
echo "=================\n\n";

// Check if we're on Windows
$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

if (!$isWindows) {
    echo "❌ This fix tool is designed for Windows XAMPP installations.\n";
    exit(1);
}

$xamppPath = 'C:\xampp';
$mysqlData = $xamppPath . '\mysql\data';

echo "⚠️  IMPORTANT: This will fix MySQL startup issues but may require backing up your data.\n";
echo "Press Enter to continue or Ctrl+C to cancel...\n";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

echo "\n1. Stopping any running MySQL processes...\n";

// Kill any running MySQL processes
$output = shell_exec('taskkill /F /IM mysqld.exe 2>nul');
if ($output) {
    echo "✅ Stopped MySQL processes\n";
} else {
    echo "ℹ️  No MySQL processes were running\n";
}

// Also try to stop MySQL service
$output = shell_exec('net stop mysql 2>nul');
if (strpos($output, 'stopped') !== false) {
    echo "✅ Stopped MySQL service\n";
} else {
    echo "ℹ️  MySQL service was not running\n";
}

echo "\n2. Backing up current InnoDB log files...\n";

$logFiles = [
    $mysqlData . '\ib_logfile0',
    $mysqlData . '\ib_logfile1'
];

$backupDir = $mysqlData . '\backup_' . date('Y-m-d_H-i-s');
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0777, true);
    echo "✅ Created backup directory: $backupDir\n";
}

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        $backupFile = $backupDir . '\\' . basename($logFile);
        if (copy($logFile, $backupFile)) {
            echo "✅ Backed up: " . basename($logFile) . "\n";
        } else {
            echo "❌ Failed to backup: " . basename($logFile) . "\n";
        }
    }
}

echo "\n3. Removing problematic InnoDB log files...\n";

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        if (unlink($logFile)) {
            echo "✅ Removed: " . basename($logFile) . "\n";
        } else {
            echo "❌ Failed to remove: " . basename($logFile) . "\n";
            echo "   You may need to remove this file manually\n";
        }
    } else {
        echo "ℹ️  File not found: " . basename($logFile) . "\n";
    }
}

echo "\n4. Checking for port conflicts...\n";

$output = shell_exec('netstat -an | findstr :3306');
if ($output) {
    echo "⚠️  Port 3306 is still in use. You may need to:\n";
    echo "   - Restart your computer\n";
    echo "   - Check for other MySQL installations\n";
    echo "   - Use Task Manager to end any remaining MySQL processes\n";
} else {
    echo "✅ Port 3306 is now free\n";
}

echo "\n5. Creating MySQL startup batch file...\n";

$startupScript = $xamppPath . '\start_mysql_safe.bat';
$batchContent = "@echo off\n";
$batchContent .= "echo Starting MySQL in safe mode...\n";
$batchContent .= "cd /d \"$xamppPath\\mysql\\bin\"\n";
$batchContent .= "mysqld --console --innodb-force-recovery=1\n";
$batchContent .= "pause\n";

if (file_put_contents($startupScript, $batchContent)) {
    echo "✅ Created startup script: $startupScript\n";
} else {
    echo "❌ Failed to create startup script\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "==============\n";
echo "1. Close XAMPP Control Panel completely\n";
echo "2. Run XAMPP Control Panel as Administrator\n";
echo "3. Try starting MySQL from XAMPP Control Panel\n\n";

echo "If MySQL still won't start:\n";
echo "4. Run the created batch file: $startupScript\n";
echo "5. If that works, stop it (Ctrl+C) and try XAMPP again\n\n";

echo "If you see 'InnoDB: Database was not shutdown normally' - that's OK!\n";
echo "MySQL will rebuild the log files automatically.\n\n";

echo "✅ MySQL fix completed!\n";
echo "💡 Remember to always run XAMPP as Administrator to avoid future issues.\n";
?>

# AI Text Detector v2.0 - Modern Detection System

A comprehensive, modern AI text detection system with advanced machine learning capabilities, beautiful UI, and robust backend architecture.

## 🚀 Features

### Core Functionality
- **Advanced AI Detection**: State-of-the-art algorithms using OpenAI GPT-4o models
- **Multi-Format Support**: Analyze text directly or upload TXT, DOCX, PDF files
- **Real-time Processing**: Instant analysis with intelligent caching
- **Detailed Analytics**: Comprehensive breakdowns with confidence scores and sub-metrics
- **Analysis History**: Complete tracking with searchable records

### Modern UI/UX
- **Responsive Design**: Mobile-first, works on all devices
- **Dark/Light Themes**: Automatic theme switching with user preferences
- **Interactive Components**: Smooth animations and transitions
- **Accessibility**: WCAG compliant with keyboard navigation
- **Progressive Web App**: Fast loading with offline capabilities

### Security & Performance
- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: API protection against abuse
- **Input Validation**: Comprehensive sanitization and validation
- **Error Handling**: Graceful error recovery and logging
- **Caching System**: Intelligent caching for improved performance

## 🏗️ Architecture

### Frontend
- **Technology**: Modern HTML5, CSS3, Vanilla JavaScript
- **Design System**: Custom CSS variables with theme support
- **Components**: Modular, reusable UI components
- **State Management**: Centralized application state

### Backend
- **API**: RESTful PHP API with proper routing
- **Database**: MySQL with optimized schema and indexing
- **Authentication**: JWT-based with refresh tokens
- **File Processing**: Support for multiple document formats
- **Logging**: Comprehensive logging and monitoring

### AI Engine
- **Model**: OpenAI GPT-4o-mini for cost-effective analysis
- **Processing**: Python-based analysis engine
- **Caching**: Result caching for improved performance
- **Analytics**: Detailed sub-scoring and reasoning

## 📦 Installation

### Prerequisites
- PHP 8.0+
- MySQL 5.7+
- Python 3.8+
- Composer
- XAMPP (for local development)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd my_freakin_thesis
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Python dependencies**
   ```bash
   pip install openai python-dotenv
   ```

4. **Database setup**
   ```bash
   mysql -u root -p < database/simple_schema.sql
   ```

5. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. **Configure web server**
   - Point document root to project directory
   - Enable URL rewriting
   - Set up API routing

### Environment Variables

Create a `.env` file with the following variables:

```env
# Database Configuration
DB_HOST=localhost
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=ai_text_detector

# Application Configuration
DEBUG=false
```

## 🎯 Usage

### Web Interface

1. **Access the application**
   - Open `login.html` in your browser
   - Create an account or login with existing credentials

2. **Analyze text**
   - Navigate to the "Analyze" section
   - Enter text directly or upload a file
   - View detailed analysis results

3. **Review history**
   - Check your analysis history
   - Export results for reporting

### API Endpoints

#### Authentication
```bash
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/forgot-password
```

#### Analysis
```bash
POST /api/analysis/text
POST /api/analysis/file
GET /api/analysis/history
GET /api/analysis/details
```

#### User Management
```bash
GET /api/user/profile
PUT /api/user/profile
GET /api/user/stats
```

### Python API

```python
from ai_engine.modern_detector import ModernAITextDetector

detector = ModernAITextDetector()
result = detector.analyze_text("Your text here")

print(f"Classification: {result.classification}")
print(f"Confidence: {result.confidence}%")
print(f"Reasoning: {result.reasoning}")
```

## 🔧 Configuration

### Database Schema

The system uses a simple database schema with the following main tables:

- `users` - User accounts and authentication (id, username, email, password, reset_token, reset_expires, created_at)
- `feedback` - User feedback and ratings (id, feedback_text, created_at)

### File Structure

```
my_freakin_thesis/
├── api/                    # API controllers and routing
│   ├── BaseController.php
│   ├── AuthController.php
│   ├── AnalysisController.php
│   └── router.php
├── ai_engine/              # Python AI analysis engine
│   └── modern_detector.py
├── config/                 # Configuration files
│   └── config.php
├── database/               # Database schema
│   └── simple_schema.sql
├── frontend/               # Modern web interface
│   ├── index.html
│   ├── assets/
│   │   ├── css/
│   │   └── js/
├── logs/                   # Application logs
├── uploads/                # File upload directory
├── vendor/                 # PHP dependencies
├── .env                    # Environment configuration
├── login.html              # Authentication page
└── README.md
```

## 🧪 Testing

### Running Tests

```bash
# PHP Unit Tests
./vendor/bin/phpunit tests/

# Python Tests
python -m pytest ai_engine/tests/

# Frontend Tests
npm test
```

### Manual Testing

1. **Authentication Flow**
   - Test registration and login
   - Verify JWT token handling
   - Test password reset functionality

2. **Analysis Features**
   - Test text input analysis
   - Test file upload analysis
   - Verify result accuracy and formatting

3. **UI/UX Testing**
   - Test responsive design on different devices
   - Verify theme switching functionality
   - Test accessibility features

## 🚀 Deployment

### Production Setup

1. **Server Requirements**
   - PHP 8.0+ with required extensions
   - MySQL 5.7+ or MariaDB 10.3+
   - Python 3.8+ with pip
   - Web server (Apache/Nginx)

2. **Security Considerations**
   - Use HTTPS in production
   - Set strong JWT secrets
   - Configure proper file permissions
   - Enable security headers

3. **Performance Optimization**
   - Enable PHP OPcache
   - Configure database indexing
   - Set up CDN for static assets
   - Implement proper caching strategies

## 📊 Monitoring

### Logging

The system provides comprehensive logging:

- `logs/analysis.log` - Analysis operations
- `logs/error.log` - Error tracking
- `logs/performance.log` - Performance metrics
- `logs/auth_error.log` - Authentication issues

### Analytics

Track system usage with built-in analytics:

- User registration and activity
- Analysis volume and patterns
- API usage and performance
- Error rates and types

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Review the logs for error details

## 🔄 Version History

### v2.0.0 (Current)
- Complete system modernization
- New UI/UX with theme support
- Enhanced API architecture
- Improved AI detection algorithms
- Comprehensive security updates

### v1.0.0 (Legacy)
- Initial AI text detection system
- Basic web interface
- OpenAI integration
- File upload support

<?php
/**
 * Simple Configuration for AI Text Detector
 * Basic configuration for thesis project
 */

class Config {
    private static $config = null;

    public static function load() {
        if (self::$config === null) {
            self::$config = [
                // Database Configuration
                'database' => [
                    'host' => 'localhost',
                    'username' => 'root',
                    'password' => '',
                    'database' => 'ai_text_detector',
                    'charset' => 'utf8mb4',
                    'options' => [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                    ]
                ],

                // Application Configuration
                'app' => [
                    'name' => 'AI Text Detector',
                    'version' => '1.0.0',
                    'debug' => false,
                ]
            ];
        }

        return self::$config;
    }

    public static function get($key, $default = null) {
        $config = self::load();
        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }
}

// Initialize configuration
Config::load();

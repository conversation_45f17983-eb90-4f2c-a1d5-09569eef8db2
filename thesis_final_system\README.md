# AI Text Detector - Thesis Final System

## Overview
This is the complete, production-ready AI Text Detection system for thesis submission. The system provides a modern web interface for detecting AI-generated text using advanced machine learning techniques.

## Features
- **Modern Authentication System**: Secure login/registration with password hashing
- **AI Text Detection**: Advanced analysis using OpenAI API with fallback mechanisms
- **User-Friendly Interface**: Modern, responsive design with dark mode support
- **Comprehensive Logging**: Detailed logging for analysis and debugging
- **Feedback System**: User feedback collection for system improvement
- **Database Integration**: MySQL database for user management and feedback storage

## System Requirements
- **Web Server**: Apache (XAMPP recommended)
- **PHP**: Version 7.4 or higher
- **MySQL**: Version 5.7 or higher
- **Python**: Version 3.7 or higher (for AI analysis)
- **Python Packages**: openai, python-dotenv (optional for enhanced features)

## Installation Instructions

### 1. Setup Web Server
1. Install XAMPP from https://www.apachefriends.org/
2. Start Apache and MySQL services
3. Copy this `thesis_final_system` folder to `C:\xampp\htdocs\`

### 2. Database Setup
1. Open phpMyAdmin (http://localhost/phpmyadmin)
2. Import the database schema:
   - Go to "Import" tab
   - Select `database/schema.sql`
   - Click "Go" to execute

### 3. Python Setup (Optional - for enhanced AI detection)
1. Install Python from https://www.python.org/
2. Install required packages:
   ```bash
   pip install openai python-dotenv
   ```
3. Create `.env` file in the root directory:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

### 4. Access the System
1. Open web browser
2. Navigate to: http://localhost/thesis_final_system/index.php
3. Use default credentials:
   - **Admin**: username: `admin`, password: `Admin123!`
   - **Test User**: username: `testuser`, password: `TestUser123!`

## File Structure
```
thesis_final_system/
├── index.php              # Login/Registration page
├── service.php            # Main AI detection service
├── logout.php             # Logout functionality
├── db.php                 # Database connection
├── ai_analysis_service.php # AI analysis PHP wrapper
├── database/
│   └── schema.sql         # Database schema
├── ai_engine/
│   └── modern_detector.py # Python AI detection engine
├── logs/                  # System logs directory
└── README.md             # This file
```

## Usage Guide

### For Users
1. **Login**: Use the login form on the main page
2. **Register**: Click "Register" tab to create new account
3. **Analyze Text**: 
   - Paste text in the analysis area
   - Click "Analyze Text"
   - View results with confidence scores
4. **Provide Feedback**: Use the feedback form to improve the system

### For Administrators
- Monitor system logs in the `logs/` directory
- Review user feedback in the database
- Check system performance metrics

## Default User Accounts
- **Administrator**
  - Username: `admin`
  - Email: `<EMAIL>`
  - Password: `Admin123!`

- **Test User**
  - Username: `testuser`
  - Email: `<EMAIL>`
  - Password: `TestUser123!`

## Technical Details

### AI Detection Methods
1. **Primary**: OpenAI API-based analysis (requires API key)
2. **Fallback**: Pattern-based heuristic analysis
3. **Metrics**: Confidence scores, processing time, detailed reasoning

### Security Features
- Password hashing using PHP's `password_hash()`
- SQL injection prevention with prepared statements
- Session management for authentication
- Input validation and sanitization

### Database Schema
- `users`: User authentication and management
- `feedback`: User feedback collection

## Troubleshooting

### Common Issues
1. **Database Connection Error**
   - Ensure MySQL is running in XAMPP
   - Check database credentials in `db.php`
   - Import the schema file

2. **Python Script Not Working**
   - System will use fallback analysis
   - Check Python installation
   - Install required packages

3. **Permission Errors**
   - Ensure `logs/` directory is writable
   - Check file permissions

### Log Files
- `logs/auth_error.log` - Authentication errors
- `logs/database_error.log` - Database connection issues
- `logs/ai_analysis.log` - AI analysis logs
- `logs/ai_detector.log` - Python script logs

## Support
For technical support or questions about this thesis project, please refer to the thesis documentation or contact the system administrator.

## License
This system is developed as part of an academic thesis project. All rights reserved.

@echo off
echo ========================================
echo AI Text Detector - Setup Script
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://www.python.org/
    pause
    exit /b 1
)

echo Python found! Installing required packages...
pip install -r requirements.txt

echo.
echo ========================================
echo Setup Instructions:
echo ========================================
echo 1. Make sure XAMPP is installed and running
echo 2. Import database/schema.sql into phpMyAdmin
echo 3. Copy .env.example to .env and add your OpenAI API key
echo 4. Access: http://localhost/thesis_final_system/index.php
echo.
echo Default login credentials:
echo Username: admin
echo Password: Admin123!
echo.
echo Setup complete! Press any key to exit...
pause >nul

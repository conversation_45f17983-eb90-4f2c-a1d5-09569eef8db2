-- Enhanced AI Text Detection System Database Schema
-- Comprehensive schema for advanced detection and analytics

CREATE DATABASE IF NOT EXISTS enhanced_ai_detector CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE enhanced_ai_detector;

-- Enhanced Users Table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('user', 'admin', 'researcher') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    reset_token VARCHAR(255),
    reset_expires DATETIM<PERSON>,
    last_login TIMES<PERSON>MP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_status (status)
);

-- Analysis Sessions Table
CREATE TABLE analysis_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INT,
    text_content LONGTEXT NOT NULL,
    text_hash VARCHAR(64) NOT NULL,
    word_count INT NOT NULL,
    character_count INT NOT NULL,
    file_name VARCHAR(255),
    file_type VARCHAR(50),
    file_size INT,
    processing_time_ms INT,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_text_hash (text_hash),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Analysis Results Table
CREATE TABLE analysis_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    classification ENUM('AI-Generated', 'Human-Written', 'Mixed', 'Professional-Human', 'Academic-Human', 'Error') NOT NULL,
    confidence DECIMAL(5,2) NOT NULL,
    reasoning LONGTEXT,
    raw_response LONGTEXT,
    is_primary_result BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_classification (classification),
    INDEX idx_confidence (confidence),
    INDEX idx_model_name (model_name)
);

-- Sub-Scores Table
CREATE TABLE sub_scores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    result_id INT NOT NULL,
    score_type VARCHAR(100) NOT NULL,
    score_value DECIMAL(3,2) NOT NULL,
    max_score DECIMAL(3,2) DEFAULT 5.00,
    description TEXT,
    FOREIGN KEY (result_id) REFERENCES analysis_results(id) ON DELETE CASCADE,
    INDEX idx_result_id (result_id),
    INDEX idx_score_type (score_type)
);

-- Professional Analysis Table
CREATE TABLE professional_analysis (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    domain_detected VARCHAR(100),
    expertise_level ENUM('novice', 'intermediate', 'expert', 'professional') DEFAULT 'novice',
    terminology_score DECIMAL(3,2),
    citation_count INT DEFAULT 0,
    structure_score DECIMAL(3,2),
    formality_score DECIMAL(3,2),
    technical_depth_score DECIMAL(3,2),
    domain_keywords JSON,
    detected_patterns JSON,
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_domain (domain_detected),
    INDEX idx_expertise_level (expertise_level)
);

-- ML Model Performance Table
CREATE TABLE ml_model_performance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    accuracy DECIMAL(5,4),
    precision_score DECIMAL(5,4),
    recall DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    training_date TIMESTAMP,
    evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    test_samples INT,
    confusion_matrix JSON,
    feature_importance JSON,
    hyperparameters JSON,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_model_name (model_name),
    INDEX idx_accuracy (accuracy),
    INDEX idx_is_active (is_active)
);

-- User Feedback Table
CREATE TABLE user_feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT,
    user_id INT,
    feedback_type ENUM('accuracy', 'false_positive', 'false_negative', 'general') NOT NULL,
    original_classification VARCHAR(50),
    suggested_classification VARCHAR(50),
    confidence_rating INT CHECK (confidence_rating BETWEEN 1 AND 5),
    feedback_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by INT,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES analysis_sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_feedback_type (feedback_type)
);

-- System Analytics Table
CREATE TABLE system_analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4),
    metric_type ENUM('counter', 'gauge', 'histogram') DEFAULT 'gauge',
    tags JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at)
);

-- API Usage Logs Table
CREATE TABLE api_usage_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_size INT,
    response_size INT,
    response_time_ms INT,
    status_code INT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at),
    INDEX idx_status_code (status_code)
);

-- Rate Limiting Table
CREATE TABLE rate_limits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    identifier VARCHAR(255) NOT NULL,
    identifier_type ENUM('user_id', 'ip_address', 'api_key') NOT NULL,
    requests_count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    window_end TIMESTAMP,
    is_blocked BOOLEAN DEFAULT FALSE,
    UNIQUE KEY unique_identifier (identifier, identifier_type),
    INDEX idx_identifier (identifier),
    INDEX idx_window_end (window_end)
);

-- Training Data Table
CREATE TABLE training_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    text_content LONGTEXT NOT NULL,
    text_hash VARCHAR(64) UNIQUE NOT NULL,
    true_label ENUM('AI-Generated', 'Human-Written', 'Mixed') NOT NULL,
    source VARCHAR(255),
    domain VARCHAR(100),
    quality_score DECIMAL(3,2),
    verified BOOLEAN DEFAULT FALSE,
    verified_by INT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_text_hash (text_hash),
    INDEX idx_true_label (true_label),
    INDEX idx_domain (domain),
    INDEX idx_verified (verified)
);

-- Create Views for Common Queries
CREATE VIEW user_analysis_summary AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(s.id) as total_analyses,
    AVG(ar.confidence) as avg_confidence,
    COUNT(CASE WHEN ar.classification = 'AI-Generated' THEN 1 END) as ai_detected,
    COUNT(CASE WHEN ar.classification = 'Human-Written' THEN 1 END) as human_detected,
    COUNT(CASE WHEN ar.classification = 'Mixed' THEN 1 END) as mixed_detected
FROM users u
LEFT JOIN analysis_sessions s ON u.id = s.user_id
LEFT JOIN analysis_results ar ON s.id = ar.session_id AND ar.is_primary_result = TRUE
GROUP BY u.id, u.username;

CREATE VIEW daily_analytics AS
SELECT 
    DATE(created_at) as analysis_date,
    COUNT(*) as total_analyses,
    AVG(processing_time_ms) as avg_processing_time,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_analyses,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_analyses
FROM analysis_sessions
GROUP BY DATE(created_at)
ORDER BY analysis_date DESC;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin', 'active', TRUE);

-- Insert sample training data
INSERT INTO training_data (text_content, text_hash, true_label, source, domain, quality_score, verified) VALUES
('The quick brown fox jumps over the lazy dog. This is a simple test sentence.', SHA2('The quick brown fox jumps over the lazy dog. This is a simple test sentence.', 256), 'Human-Written', 'manual', 'general', 4.5, TRUE),
('In conclusion, the implementation of artificial intelligence systems requires careful consideration of ethical implications and potential societal impacts.', SHA2('In conclusion, the implementation of artificial intelligence systems requires careful consideration of ethical implications and potential societal impacts.', 256), 'AI-Generated', 'synthetic', 'technology', 4.0, TRUE);

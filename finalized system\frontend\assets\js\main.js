/**
 * Enhanced AI Detection System - Main JavaScript
 * Core functionality and navigation
 */

class EnhancedAIDetector {
    constructor() {
        this.currentSection = 'dashboard';
        this.apiBaseUrl = '../backend/api';
        this.sessionId = null;
        this.analysisHistory = [];
        
        this.init();
    }
    
    init() {
        this.setupNavigation();
        this.setupEventListeners();
        this.loadUserPreferences();
        this.generateSessionId();
        
        // Initialize dashboard by default
        this.showSection('dashboard');
    }
    
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
                this.updateActiveNavLink(link);
            });
        });
    }
    
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
            
            // Load section-specific data
            this.loadSectionData(sectionName);
        }
    }
    
    updateActiveNavLink(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }
    
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'analytics':
                this.loadAnalyticsData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }
    
    setupEventListeners() {
        // Theme toggle
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.setTheme(e.target.value);
            });
        }
        
        // Confidence threshold
        const confidenceThreshold = document.getElementById('confidence-threshold');
        const thresholdValue = document.getElementById('threshold-value');
        if (confidenceThreshold && thresholdValue) {
            confidenceThreshold.addEventListener('input', (e) => {
                thresholdValue.textContent = e.target.value;
                this.saveUserPreference('confidenceThreshold', e.target.value);
            });
        }
        
        // Auto-save setting
        const autoSave = document.getElementById('auto-save');
        if (autoSave) {
            autoSave.addEventListener('change', (e) => {
                this.saveUserPreference('autoSave', e.target.checked);
            });
        }
        
        // Window resize handler
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    generateSessionId() {
        this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async loadDashboardData() {
        try {
            this.showLoading();
            
            // Load user statistics
            const stats = await this.apiCall('/api/user/stats');
            this.updateDashboardStats(stats);
            
            // Load recent analyses
            const recentAnalyses = await this.apiCall('/api/analyses/recent', { limit: 10 });
            this.updateRecentAnalyses(recentAnalyses);
            
            // Load accuracy trends
            const trends = await this.apiCall('/api/analytics/trends', { period: '7d' });
            this.updateAccuracyChart(trends);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data');
        } finally {
            this.hideLoading();
        }
    }
    
    updateDashboardStats(stats) {
        const elements = {
            'total-analyses': stats.totalAnalyses || 0,
            'ai-detected': stats.aiDetected || 0,
            'professional-detected': stats.professionalDetected || 0,
            'avg-confidence': Math.round((stats.avgConfidence || 0) * 100) + '%'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }
    
    updateRecentAnalyses(analyses) {
        const container = document.getElementById('recent-analyses');
        if (!container) return;
        
        if (!analyses || analyses.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-chart-line"></i>
                    <h3>No analyses yet</h3>
                    <p>Start by analyzing some text to see your results here.</p>
                    <button class="btn btn-primary" onclick="app.showSection('analyze')">
                        <i class="fas fa-plus"></i>
                        Start Analysis
                    </button>
                </div>
            `;
            return;
        }
        
        container.innerHTML = analyses.map(analysis => `
            <div class="analysis-item fade-in">
                <div class="analysis-info">
                    <h4>${this.truncateText(analysis.textPreview || 'Text Analysis', 50)}</h4>
                    <div class="analysis-meta">
                        <span><i class="fas fa-clock"></i> ${this.formatDate(analysis.createdAt)}</span>
                        <span><i class="fas fa-file-text"></i> ${analysis.wordCount} words</span>
                        ${analysis.domain ? `<span><i class="fas fa-tag"></i> ${analysis.domain}</span>` : ''}
                    </div>
                </div>
                <div class="analysis-result">
                    <div class="result-badge ${this.getClassificationClass(analysis.classification)}">
                        ${analysis.classification}
                    </div>
                    <div class="confidence-indicator">
                        ${Math.round(analysis.confidence * 100)}%
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${analysis.confidence * 100}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    async loadAnalyticsData() {
        try {
            this.showLoading();
            
            const timeRange = document.getElementById('time-range')?.value || '7d';
            const analysisType = document.getElementById('analysis-type')?.value || 'all';
            
            // Load analytics data
            const analytics = await this.apiCall('/api/analytics/dashboard', {
                timeRange,
                analysisType
            });
            
            this.updateAnalyticsCharts(analytics);
            this.updatePerformanceMetrics(analytics.performance);
            
        } catch (error) {
            console.error('Failed to load analytics data:', error);
            this.showError('Failed to load analytics data');
        } finally {
            this.hideLoading();
        }
    }
    
    loadSettingsData() {
        const preferences = this.getUserPreferences();
        
        // Update form values
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) themeSelect.value = preferences.theme || 'light';
        
        const confidenceThreshold = document.getElementById('confidence-threshold');
        const thresholdValue = document.getElementById('threshold-value');
        if (confidenceThreshold && thresholdValue) {
            confidenceThreshold.value = preferences.confidenceThreshold || 0.7;
            thresholdValue.textContent = preferences.confidenceThreshold || 0.7;
        }
        
        const autoSave = document.getElementById('auto-save');
        if (autoSave) autoSave.checked = preferences.autoSave || false;
    }
    
    async apiCall(endpoint, data = null, method = 'GET') {
        let url = `${this.apiBaseUrl}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'X-Session-ID': this.sessionId
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        } else if (data && method === 'GET') {
            const params = new URLSearchParams(data);
            url += '?' + params.toString();
        }

        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }
    
    showLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }
    
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    showError(message) {
        // Create or update error notification
        this.showNotification(message, 'error');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelector('.notification');
        if (existing) existing.remove();
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Utility functions
    animateNumber(element, targetValue) {
        const isPercentage = typeof targetValue === 'string' && targetValue.includes('%');
        const numericValue = isPercentage ? parseInt(targetValue) : targetValue;
        const currentValue = parseInt(element.textContent) || 0;
        
        const increment = (numericValue - currentValue) / 20;
        let current = currentValue;
        
        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= numericValue) || (increment < 0 && current <= numericValue)) {
                current = numericValue;
                clearInterval(timer);
            }
            
            element.textContent = isPercentage ? Math.round(current) + '%' : Math.round(current);
        }, 50);
    }
    
    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return date.toLocaleDateString();
    }
    
    getClassificationClass(classification) {
        const classMap = {
            'AI-Generated': 'ai',
            'Human-Written': 'human',
            'Professional-Human': 'professional',
            'Academic-Human': 'academic',
            'Uncertain': 'uncertain'
        };
        return classMap[classification] || 'uncertain';
    }
    
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.saveUserPreference('theme', theme);
    }
    
    saveUserPreference(key, value) {
        const preferences = this.getUserPreferences();
        preferences[key] = value;
        localStorage.setItem('aiDetectorPreferences', JSON.stringify(preferences));
    }
    
    getUserPreferences() {
        const stored = localStorage.getItem('aiDetectorPreferences');
        return stored ? JSON.parse(stored) : {};
    }
    
    loadUserPreferences() {
        const preferences = this.getUserPreferences();
        
        // Apply theme
        if (preferences.theme) {
            this.setTheme(preferences.theme);
        }
    }
    
    handleResize() {
        // Handle responsive behavior
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile', isMobile);
    }
}

// Initialize the application
const app = new EnhancedAIDetector();

// Export for global access
window.app = app;

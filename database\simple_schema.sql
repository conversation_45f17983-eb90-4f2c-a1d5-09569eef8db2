-- Simple AI Text Detector Database Schema
-- Basic schema for thesis project - only essential tables

CREATE DATABASE IF NOT EXISTS ai_text_detector CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ai_text_detector;

-- Basic users table for authentication
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Basic feedback table
CREATE TABLE feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    feedback_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

<?php
/**
 * Database Connection - Updated Configuration
 */

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "ai_text_detector";
$port = 3306;

// Create logs directory if it doesn't exist
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

// Create connection with port
try {
    $conn = new mysqli($servername, $username, $password, $dbname, $port);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
} catch (Exception $e) {
    error_log("Database connection failed: " . $e->getMessage(), 3, "logs/database_error.log");
    die("Database connection failed. Please try again later.");
}

// Function for modern code compatibility
function getDbConnection() {
    global $conn;
    return $conn;
}
?>

/* Enhanced AI Detection System - Component Styles */

/* Analysis Input Components */
.analysis-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-8);
    margin-top: var(--spacing-6);
}

.analysis-input {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.input-methods {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: var(--spacing-4);
}

.method-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    background: transparent;
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: 500;
}

.method-btn:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.method-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.input-panel {
    display: none;
}

.input-panel.active {
    display: block;
}

.input-stats {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* File Upload Components */
.file-drop-zone {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    background-opacity: 0.05;
}

.file-drop-zone i {
    font-size: var(--font-size-4xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
}

.file-drop-zone p {
    margin-bottom: var(--spacing-2);
    color: var(--gray-600);
    font-weight: 500;
}

.file-drop-zone input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.supported-formats {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.uploaded-files {
    margin-top: var(--spacing-4);
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-2);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.file-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.file-details h4 {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-900);
}

.file-details p {
    margin: 0;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* URL Input Components */
.url-input-group {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
}

.url-input-group input {
    flex: 1;
}

.url-preview {
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.url-preview.loading {
    text-align: center;
    color: var(--gray-500);
}

/* Analysis Options */
.analysis-options {
    margin: var(--spacing-6) 0;
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.analysis-options h4 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);
}

.option-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
}

.option-item:hover {
    background: var(--white);
}

.option-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    font-weight: 500;
    color: var(--gray-700);
}

/* Analysis Results */
.analysis-results {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.results-placeholder {
    text-align: center;
    color: var(--gray-500);
}

.results-placeholder i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

.results-placeholder h3 {
    margin-bottom: var(--spacing-2);
    color: var(--gray-600);
}

.results-content {
    padding: var(--spacing-6);
    width: 100%;
}

/* Result Cards */
.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.result-classification {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.classification-badge {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.classification-badge.ai-generated {
    background: var(--danger-color);
    color: var(--white);
}

.classification-badge.human-written {
    background: var(--success-color);
    color: var(--white);
}

.classification-badge.professional-human {
    background: var(--primary-color);
    color: var(--white);
}

.classification-badge.academic-human {
    background: var(--accent-color);
    color: var(--white);
}

.classification-badge.uncertain {
    background: var(--warning-color);
    color: var(--white);
}

.confidence-score {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

/* Score Breakdown */
.score-breakdown {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
    margin: var(--spacing-6) 0;
}

.score-item {
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.score-item h4 {
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
    font-size: var(--font-size-sm);
}

.score-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.score-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-slow);
}

.score-value {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

/* Professional Analysis */
.professional-analysis {
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.professional-analysis h4 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-4);
}

.analysis-metric {
    text-align: center;
    padding: var(--spacing-3);
    background: var(--white);
    border-radius: var(--radius-lg);
}

.analysis-metric .metric-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-1);
}

.analysis-metric .metric-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* Reasoning Section */
.reasoning-section {
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
}

.reasoning-section h4 {
    margin-bottom: var(--spacing-3);
    color: var(--gray-900);
}

.reasoning-list {
    list-style: none;
}

.reasoning-list li {
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-700);
}

.reasoning-list li:last-child {
    border-bottom: none;
}

.reasoning-list li::before {
    content: "•";
    color: var(--primary-color);
    margin-right: var(--spacing-2);
    font-weight: bold;
}

/* Batch Processing Components */
.batch-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-8);
}

.batch-upload {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.upload-area {
    text-align: center;
    padding: var(--spacing-8);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-6);
}

.upload-area i {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.batch-options {
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.option-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
}

.batch-queue {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.queue-list {
    max-height: 400px;
    overflow-y: auto;
}

.queue-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-3);
}

.queue-item.processing {
    border-color: var(--primary-color);
    background: var(--primary-color);
    background-opacity: 0.05;
}

.queue-item.completed {
    border-color: var(--success-color);
    background: var(--success-color);
    background-opacity: 0.05;
}

.queue-item.failed {
    border-color: var(--danger-color);
    background: var(--danger-color);
    background-opacity: 0.05;
}

.queue-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.status-icon.pending {
    background: var(--gray-400);
}

.status-icon.processing {
    background: var(--primary-color);
    animation: pulse 2s infinite;
}

.status-icon.completed {
    background: var(--success-color);
}

.status-icon.failed {
    background: var(--danger-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design for Components */
@media (max-width: 1024px) {
    .analysis-container,
    .batch-container {
        grid-template-columns: 1fr;
    }
    
    .score-breakdown,
    .analysis-grid {
        grid-template-columns: 1fr;
    }
    
    .options-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }
    
    .analysis-input,
    .batch-upload,
    .batch-queue {
        padding: var(--spacing-4);
    }
}

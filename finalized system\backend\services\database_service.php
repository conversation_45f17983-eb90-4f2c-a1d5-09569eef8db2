<?php
/**
 * Enhanced Database Service
 * Comprehensive database operations for the enhanced AI detection system
 */

require_once __DIR__ . '/../../config/config.php';

class EnhancedDatabaseService {
    private $pdo;
    private $logger;
    
    public function __construct() {
        $this->initializeDatabase();
        $this->setupLogging();
    }
    
    private function initializeDatabase() {
        try {
            $config = EnhancedConfig::get('database');
            
            $dsn = sprintf(
                "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                $config['host'],
                $config['port'],
                $config['name'],
                $config['charset']
            );
            
            $this->pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    private function setupLogging() {
        // Simple error logging for now
        $this->logger = (object) [
            'error' => function($message) {
                error_log("[DATABASE ERROR] " . $message, 3, 'logs/database_error.log');
            },
            'info' => function($message) {
                error_log("[DATABASE INFO] " . $message, 3, 'logs/database.log');
            }
        ];
    }
    
    /**
     * User Management Methods
     */
    public function createUser($userData) {
        try {
            $sql = "INSERT INTO users (username, email, password, first_name, last_name, role, preferences) 
                    VALUES (:username, :email, :password, :first_name, :last_name, :role, :preferences)";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                'username' => $userData['username'],
                'email' => $userData['email'],
                'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
                'first_name' => $userData['first_name'] ?? null,
                'last_name' => $userData['last_name'] ?? null,
                'role' => $userData['role'] ?? 'user',
                'preferences' => json_encode($userData['preferences'] ?? [])
            ]);
            
            if ($result) {
                return $this->pdo->lastInsertId();
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logger->error("User creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function getUserById($userId) {
        try {
            $sql = "SELECT * FROM users WHERE id = :id AND status = 'active'";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['id' => $userId]);
            
            $user = $stmt->fetch();
            if ($user && $user['preferences']) {
                $user['preferences'] = json_decode($user['preferences'], true);
            }
            
            return $user;
            
        } catch (PDOException $e) {
            $this->logger->error("Get user failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function updateUserLastLogin($userId) {
        try {
            $sql = "UPDATE users SET last_login = CURRENT_TIMESTAMP, login_attempts = 0 WHERE id = :id";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute(['id' => $userId]);
            
        } catch (PDOException $e) {
            $this->logger->error("Update last login failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Analysis Session Methods
     */
    public function createAnalysisSession($sessionData) {
        try {
            $sql = "INSERT INTO analysis_sessions 
                    (session_id, user_id, text_content, text_hash, word_count, character_count, 
                     file_name, file_type, file_size, status) 
                    VALUES (:session_id, :user_id, :text_content, :text_hash, :word_count, 
                            :character_count, :file_name, :file_type, :file_size, :status)";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                'session_id' => $sessionData['session_id'],
                'user_id' => $sessionData['user_id'] ?? null,
                'text_content' => $sessionData['text_content'],
                'text_hash' => hash('sha256', $sessionData['text_content']),
                'word_count' => $sessionData['word_count'],
                'character_count' => $sessionData['character_count'],
                'file_name' => $sessionData['file_name'] ?? null,
                'file_type' => $sessionData['file_type'] ?? null,
                'file_size' => $sessionData['file_size'] ?? null,
                'status' => $sessionData['status'] ?? 'pending'
            ]);
            
            if ($result) {
                return $this->pdo->lastInsertId();
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logger->error("Create analysis session failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function updateAnalysisSession($sessionId, $updateData) {
        try {
            $setParts = [];
            $params = ['session_id' => $sessionId];
            
            foreach ($updateData as $key => $value) {
                $setParts[] = "$key = :$key";
                $params[$key] = $value;
            }
            
            $sql = "UPDATE analysis_sessions SET " . implode(', ', $setParts) . " WHERE session_id = :session_id";
            $stmt = $this->pdo->prepare($sql);
            
            return $stmt->execute($params);
            
        } catch (PDOException $e) {
            $this->logger->error("Update analysis session failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function getAnalysisSession($sessionId) {
        try {
            $sql = "SELECT * FROM analysis_sessions WHERE session_id = :session_id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['session_id' => $sessionId]);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            $this->logger->error("Get analysis session failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Analysis Results Methods
     */
    public function saveAnalysisResult($resultData) {
        try {
            $sql = "INSERT INTO analysis_results 
                    (session_id, model_name, classification, confidence, reasoning, raw_response, is_primary_result) 
                    VALUES (:session_id, :model_name, :classification, :confidence, :reasoning, :raw_response, :is_primary_result)";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                'session_id' => $resultData['session_id'],
                'model_name' => $resultData['model_name'],
                'classification' => $resultData['classification'],
                'confidence' => $resultData['confidence'],
                'reasoning' => $resultData['reasoning'],
                'raw_response' => $resultData['raw_response'] ?? null,
                'is_primary_result' => $resultData['is_primary_result'] ?? false
            ]);
            
            if ($result) {
                return $this->pdo->lastInsertId();
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logger->error("Save analysis result failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function saveSubScores($resultId, $subScores) {
        try {
            $this->pdo->beginTransaction();
            
            $sql = "INSERT INTO sub_scores (result_id, score_type, score_value, description) 
                    VALUES (:result_id, :score_type, :score_value, :description)";
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($subScores as $scoreType => $scoreValue) {
                $stmt->execute([
                    'result_id' => $resultId,
                    'score_type' => $scoreType,
                    'score_value' => $scoreValue,
                    'description' => null
                ]);
            }
            
            $this->pdo->commit();
            return true;
            
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            $this->logger->error("Save sub-scores failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function saveProfessionalAnalysis($sessionId, $professionalData) {
        try {
            $sql = "INSERT INTO professional_analysis 
                    (session_id, domain_detected, expertise_level, terminology_score, citation_count, 
                     structure_score, formality_score, technical_depth_score, domain_keywords, detected_patterns) 
                    VALUES (:session_id, :domain_detected, :expertise_level, :terminology_score, :citation_count, 
                            :structure_score, :formality_score, :technical_depth_score, :domain_keywords, :detected_patterns)";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                'session_id' => $sessionId,
                'domain_detected' => $professionalData['domain_detected'] ?? null,
                'expertise_level' => $professionalData['expertise_level'] ?? 'novice',
                'terminology_score' => $professionalData['terminology_score'] ?? 0,
                'citation_count' => $professionalData['citation_count'] ?? 0,
                'structure_score' => $professionalData['structure_score'] ?? 0,
                'formality_score' => $professionalData['formality_score'] ?? 0,
                'technical_depth_score' => $professionalData['technical_depth_score'] ?? 0,
                'domain_keywords' => json_encode($professionalData['domain_keywords'] ?? []),
                'detected_patterns' => json_encode($professionalData['detected_patterns'] ?? [])
            ]);
            
        } catch (PDOException $e) {
            $this->logger->error("Save professional analysis failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Analytics and Reporting Methods
     */
    public function getUserAnalyticsSummary($userId, $dateRange = null) {
        try {
            $sql = "SELECT 
                        COUNT(s.id) as total_analyses,
                        AVG(ar.confidence) as avg_confidence,
                        COUNT(CASE WHEN ar.classification = 'AI-Generated' THEN 1 END) as ai_detected,
                        COUNT(CASE WHEN ar.classification = 'Human-Written' THEN 1 END) as human_detected,
                        COUNT(CASE WHEN ar.classification = 'Professional-Human' THEN 1 END) as professional_detected,
                        COUNT(CASE WHEN ar.classification = 'Academic-Human' THEN 1 END) as academic_detected,
                        AVG(s.processing_time_ms) as avg_processing_time
                    FROM analysis_sessions s
                    LEFT JOIN analysis_results ar ON s.id = ar.session_id AND ar.is_primary_result = TRUE
                    WHERE s.user_id = :user_id";
            
            $params = ['user_id' => $userId];
            
            if ($dateRange) {
                $sql .= " AND s.created_at BETWEEN :start_date AND :end_date";
                $params['start_date'] = $dateRange['start'];
                $params['end_date'] = $dateRange['end'];
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            $this->logger->error("Get user analytics failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function getSystemAnalytics($dateRange = null) {
        try {
            $sql = "SELECT 
                        COUNT(s.id) as total_analyses,
                        COUNT(DISTINCT s.user_id) as unique_users,
                        AVG(ar.confidence) as avg_confidence,
                        AVG(s.processing_time_ms) as avg_processing_time,
                        COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as successful_analyses,
                        COUNT(CASE WHEN s.status = 'failed' THEN 1 END) as failed_analyses
                    FROM analysis_sessions s
                    LEFT JOIN analysis_results ar ON s.id = ar.session_id AND ar.is_primary_result = TRUE";
            
            $params = [];
            
            if ($dateRange) {
                $sql .= " WHERE s.created_at BETWEEN :start_date AND :end_date";
                $params['start_date'] = $dateRange['start'];
                $params['end_date'] = $dateRange['end'];
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            $this->logger->error("Get system analytics failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Model Performance Tracking Methods
     */
    public function saveModelPerformance($performanceData) {
        try {
            $sql = "INSERT INTO ml_model_performance
                    (model_name, model_version, accuracy, precision_score, recall, f1_score,
                     training_date, test_samples, confusion_matrix, feature_importance, hyperparameters)
                    VALUES (:model_name, :model_version, :accuracy, :precision_score, :recall, :f1_score,
                            :training_date, :test_samples, :confusion_matrix, :feature_importance, :hyperparameters)";

            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                'model_name' => $performanceData['model_name'],
                'model_version' => $performanceData['model_version'],
                'accuracy' => $performanceData['accuracy'],
                'precision_score' => $performanceData['precision_score'],
                'recall' => $performanceData['recall'],
                'f1_score' => $performanceData['f1_score'],
                'training_date' => $performanceData['training_date'],
                'test_samples' => $performanceData['test_samples'],
                'confusion_matrix' => json_encode($performanceData['confusion_matrix']),
                'feature_importance' => json_encode($performanceData['feature_importance']),
                'hyperparameters' => json_encode($performanceData['hyperparameters'])
            ]);

        } catch (PDOException $e) {
            $this->logger->error("Save model performance failed: " . $e->getMessage());
            return false;
        }
    }

    public function getModelPerformanceHistory($modelName, $limit = 10) {
        try {
            $sql = "SELECT * FROM ml_model_performance
                    WHERE model_name = :model_name
                    ORDER BY evaluation_date DESC
                    LIMIT :limit";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(':model_name', $modelName);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll();

            // Decode JSON fields
            foreach ($results as &$result) {
                $result['confusion_matrix'] = json_decode($result['confusion_matrix'], true);
                $result['feature_importance'] = json_decode($result['feature_importance'], true);
                $result['hyperparameters'] = json_decode($result['hyperparameters'], true);
            }

            return $results;

        } catch (PDOException $e) {
            $this->logger->error("Get model performance history failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * User Feedback Methods
     */
    public function saveFeedback($feedbackData) {
        try {
            $sql = "INSERT INTO user_feedback
                    (session_id, user_id, feedback_type, original_classification, suggested_classification,
                     confidence_rating, feedback_text)
                    VALUES (:session_id, :user_id, :feedback_type, :original_classification, :suggested_classification,
                            :confidence_rating, :feedback_text)";

            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                'session_id' => $feedbackData['session_id'],
                'user_id' => $feedbackData['user_id'],
                'feedback_type' => $feedbackData['feedback_type'],
                'original_classification' => $feedbackData['original_classification'],
                'suggested_classification' => $feedbackData['suggested_classification'],
                'confidence_rating' => $feedbackData['confidence_rating'],
                'feedback_text' => $feedbackData['feedback_text']
            ]);

        } catch (PDOException $e) {
            $this->logger->error("Save feedback failed: " . $e->getMessage());
            return false;
        }
    }

    public function getFeedbackForTraining($verified = true, $limit = 1000) {
        try {
            $sql = "SELECT f.*, s.text_content, s.text_hash
                    FROM user_feedback f
                    JOIN analysis_sessions s ON f.session_id = s.id
                    WHERE f.is_verified = :verified
                    ORDER BY f.created_at DESC
                    LIMIT :limit";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(':verified', $verified, PDO::PARAM_BOOL);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll();

        } catch (PDOException $e) {
            $this->logger->error("Get feedback for training failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Training Data Methods
     */
    public function saveTrainingData($trainingData) {
        try {
            $sql = "INSERT INTO training_data
                    (text_content, text_hash, true_label, source, domain, quality_score, metadata)
                    VALUES (:text_content, :text_hash, :true_label, :source, :domain, :quality_score, :metadata)
                    ON DUPLICATE KEY UPDATE
                    true_label = VALUES(true_label),
                    quality_score = VALUES(quality_score),
                    metadata = VALUES(metadata)";

            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                'text_content' => $trainingData['text_content'],
                'text_hash' => hash('sha256', $trainingData['text_content']),
                'true_label' => $trainingData['true_label'],
                'source' => $trainingData['source'],
                'domain' => $trainingData['domain'],
                'quality_score' => $trainingData['quality_score'],
                'metadata' => json_encode($trainingData['metadata'] ?? [])
            ]);

        } catch (PDOException $e) {
            $this->logger->error("Save training data failed: " . $e->getMessage());
            return false;
        }
    }

    public function getTrainingData($domain = null, $verified = true, $limit = 10000) {
        try {
            $sql = "SELECT * FROM training_data WHERE verified = :verified";
            $params = ['verified' => $verified];

            if ($domain) {
                $sql .= " AND domain = :domain";
                $params['domain'] = $domain;
            }

            $sql .= " ORDER BY created_at DESC LIMIT :limit";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(':verified', $verified, PDO::PARAM_BOOL);
            if ($domain) {
                $stmt->bindValue(':domain', $domain);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll();

            // Decode metadata
            foreach ($results as &$result) {
                $result['metadata'] = json_decode($result['metadata'], true);
            }

            return $results;

        } catch (PDOException $e) {
            $this->logger->error("Get training data failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Utility Methods
     */
    public function getConnectionStatus() {
        try {
            $stmt = $this->pdo->query("SELECT 1");
            return $stmt !== false;
        } catch (PDOException $e) {
            return false;
        }
    }

    public function __destruct() {
        $this->pdo = null;
    }
}
?>

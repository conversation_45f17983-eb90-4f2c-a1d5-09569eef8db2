#!/usr/bin/env python3
"""
Professional Writing Analysis Service
Advanced algorithms for analyzing professional and academic writing patterns
"""

import re
import json
import spacy
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from collections import Counter
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import sent_tokenize, word_tokenize
from textstat import flesch_reading_ease, flesch_kincaid_grade, automated_readability_index

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

@dataclass
class ProfessionalAnalysisResult:
    """Comprehensive professional writing analysis result"""
    domain_detected: str
    expertise_level: str
    authenticity_score: float
    professional_indicators: Dict[str, Any]
    linguistic_features: Dict[str, float]
    domain_expertise: Dict[str, Any]
    writing_style: Dict[str, Any]
    credibility_markers: Dict[str, Any]
    ai_likelihood_factors: List[str]
    human_likelihood_factors: List[str]

class DomainExpertiseAnalyzer:
    """Analyzes domain-specific expertise and knowledge depth"""
    
    def __init__(self):
        self.domain_vocabularies = {
            'academic': {
                'keywords': ['research', 'methodology', 'hypothesis', 'literature', 'peer-reviewed', 
                           'empirical', 'theoretical', 'analysis', 'findings', 'conclusion', 'abstract',
                           'bibliography', 'citation', 'dissertation', 'thesis', 'journal', 'publication'],
                'phrases': ['according to', 'previous studies', 'further research', 'significant difference',
                          'statistical analysis', 'data collection', 'sample size', 'control group'],
                'structures': ['introduction', 'methodology', 'results', 'discussion', 'conclusion']
            },
            'legal': {
                'keywords': ['pursuant', 'whereas', 'heretofore', 'plaintiff', 'defendant', 'jurisdiction',
                           'statute', 'precedent', 'jurisprudence', 'litigation', 'contract', 'liability',
                           'negligence', 'damages', 'injunction', 'subpoena', 'deposition'],
                'phrases': ['in accordance with', 'subject to', 'notwithstanding', 'provided that',
                          'it is hereby', 'shall be deemed', 'force and effect'],
                'structures': ['whereas clauses', 'numbered paragraphs', 'defined terms']
            },
            'medical': {
                'keywords': ['diagnosis', 'treatment', 'patient', 'symptoms', 'clinical', 'therapeutic',
                           'pathology', 'etiology', 'prognosis', 'differential', 'contraindication',
                           'pharmacology', 'dosage', 'adverse', 'efficacy', 'protocol'],
                'phrases': ['patient presents with', 'differential diagnosis', 'treatment protocol',
                          'clinical trial', 'adverse effects', 'therapeutic intervention'],
                'structures': ['case presentation', 'treatment plan', 'follow-up']
            },
            'technical': {
                'keywords': ['implementation', 'algorithm', 'optimization', 'framework', 'architecture',
                           'specification', 'protocol', 'interface', 'deployment', 'scalability',
                           'performance', 'debugging', 'configuration', 'integration'],
                'phrases': ['system architecture', 'performance optimization', 'code implementation',
                          'technical specification', 'software development', 'data structure'],
                'structures': ['technical documentation', 'code examples', 'system diagrams']
            },
            'business': {
                'keywords': ['stakeholder', 'revenue', 'strategy', 'market', 'competitive', 'ROI', 'KPI',
                           'synergy', 'leverage', 'optimization', 'scalability', 'monetization',
                           'acquisition', 'valuation', 'portfolio', 'diversification'],
                'phrases': ['market analysis', 'competitive advantage', 'business model', 'value proposition',
                          'strategic planning', 'financial performance', 'customer acquisition'],
                'structures': ['executive summary', 'financial projections', 'market analysis']
            },
            'scientific': {
                'keywords': ['experiment', 'hypothesis', 'variable', 'control', 'observation', 'measurement',
                           'correlation', 'causation', 'significance', 'replication', 'peer-review',
                           'methodology', 'protocol', 'specimen', 'analysis'],
                'phrases': ['experimental design', 'statistical significance', 'control group',
                          'data analysis', 'research methodology', 'peer review'],
                'structures': ['abstract', 'introduction', 'methods', 'results', 'discussion']
            }
        }
        
        self.expertise_indicators = {
            'novice': {
                'characteristics': ['basic terminology', 'general statements', 'limited depth'],
                'patterns': [r'\b(basic|simple|easy|general)\b', r'\b(I think|maybe|probably)\b']
            },
            'intermediate': {
                'characteristics': ['some technical terms', 'moderate complexity', 'structured approach'],
                'patterns': [r'\b(however|therefore|furthermore)\b', r'\b(analysis|evaluation)\b']
            },
            'advanced': {
                'characteristics': ['specialized terminology', 'complex concepts', 'nuanced understanding'],
                'patterns': [r'\b(sophisticated|comprehensive|intricate)\b', r'\b(paradigm|framework|methodology)\b']
            },
            'expert': {
                'characteristics': ['cutting-edge knowledge', 'original insights', 'field-specific jargon'],
                'patterns': [r'\b(novel|innovative|groundbreaking)\b', r'\b(state-of-the-art|cutting-edge)\b']
            }
        }
    
    def analyze_domain_expertise(self, text: str) -> Dict[str, Any]:
        """Analyze domain expertise and knowledge depth"""
        text_lower = text.lower()
        
        # Detect primary domain
        domain_scores = {}
        for domain, vocab in self.domain_vocabularies.items():
            score = 0
            
            # Count keywords
            keyword_count = sum(1 for keyword in vocab['keywords'] if keyword in text_lower)
            score += keyword_count * 2
            
            # Count phrases
            phrase_count = sum(1 for phrase in vocab['phrases'] if phrase in text_lower)
            score += phrase_count * 3
            
            # Check for structural elements
            structure_count = sum(1 for structure in vocab['structures'] if structure in text_lower)
            score += structure_count * 1
            
            domain_scores[domain] = score
        
        primary_domain = max(domain_scores, key=domain_scores.get) if domain_scores else 'general'
        domain_confidence = domain_scores.get(primary_domain, 0) / max(sum(domain_scores.values()), 1)
        
        # Assess expertise level
        expertise_level = self._assess_expertise_level(text, primary_domain)
        
        # Analyze knowledge depth
        knowledge_depth = self._analyze_knowledge_depth(text, primary_domain)
        
        return {
            'primary_domain': primary_domain,
            'domain_confidence': domain_confidence,
            'domain_scores': domain_scores,
            'expertise_level': expertise_level,
            'knowledge_depth': knowledge_depth,
            'domain_keywords_found': self._extract_domain_keywords(text, primary_domain),
            'technical_complexity': self._assess_technical_complexity(text)
        }
    
    def _assess_expertise_level(self, text: str, domain: str) -> str:
        """Assess the expertise level of the writing"""
        text_lower = text.lower()
        
        # Count indicators for each expertise level
        level_scores = {}
        for level, indicators in self.expertise_indicators.items():
            score = 0
            for pattern in indicators['patterns']:
                matches = len(re.findall(pattern, text_lower))
                score += matches
            level_scores[level] = score
        
        # Additional domain-specific assessment
        if domain in self.domain_vocabularies:
            vocab = self.domain_vocabularies[domain]
            advanced_terms = sum(1 for keyword in vocab['keywords'][-5:] if keyword in text_lower)
            if advanced_terms >= 3:
                level_scores['expert'] += 2
            elif advanced_terms >= 2:
                level_scores['advanced'] += 1
        
        # Determine final expertise level
        if not level_scores or max(level_scores.values()) == 0:
            return 'novice'
        
        return max(level_scores, key=level_scores.get)
    
    def _analyze_knowledge_depth(self, text: str, domain: str) -> Dict[str, Any]:
        """Analyze the depth of knowledge demonstrated"""
        depth_indicators = {
            'surface_level': 0,
            'moderate_depth': 0,
            'deep_knowledge': 0,
            'expert_insight': 0
        }
        
        # Surface level indicators
        surface_patterns = [r'\b(basic|simple|general|common)\b', r'\b(everyone knows|obviously)\b']
        for pattern in surface_patterns:
            depth_indicators['surface_level'] += len(re.findall(pattern, text.lower()))
        
        # Moderate depth indicators
        moderate_patterns = [r'\b(analysis|evaluation|comparison)\b', r'\b(however|therefore|consequently)\b']
        for pattern in moderate_patterns:
            depth_indicators['moderate_depth'] += len(re.findall(pattern, text.lower()))
        
        # Deep knowledge indicators
        deep_patterns = [r'\b(sophisticated|comprehensive|nuanced)\b', r'\b(implications|ramifications)\b']
        for pattern in deep_patterns:
            depth_indicators['deep_knowledge'] += len(re.findall(pattern, text.lower()))
        
        # Expert insight indicators
        expert_patterns = [r'\b(novel|innovative|paradigm)\b', r'\b(cutting-edge|state-of-the-art)\b']
        for pattern in expert_patterns:
            depth_indicators['expert_insight'] += len(re.findall(pattern, text.lower()))
        
        # Calculate depth score
        total_indicators = sum(depth_indicators.values())
        if total_indicators == 0:
            depth_score = 1.0  # Default to surface level
        else:
            depth_score = (
                depth_indicators['surface_level'] * 1 +
                depth_indicators['moderate_depth'] * 2 +
                depth_indicators['deep_knowledge'] * 3 +
                depth_indicators['expert_insight'] * 4
            ) / total_indicators
        
        return {
            'depth_score': depth_score,
            'indicators': depth_indicators,
            'primary_depth_level': max(depth_indicators, key=depth_indicators.get)
        }
    
    def _extract_domain_keywords(self, text: str, domain: str) -> List[str]:
        """Extract domain-specific keywords found in the text"""
        if domain not in self.domain_vocabularies:
            return []
        
        text_lower = text.lower()
        vocab = self.domain_vocabularies[domain]
        found_keywords = []
        
        for keyword in vocab['keywords']:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        for phrase in vocab['phrases']:
            if phrase in text_lower:
                found_keywords.append(phrase)
        
        return found_keywords
    
    def _assess_technical_complexity(self, text: str) -> float:
        """Assess the technical complexity of the text"""
        complexity_score = 0
        
        # Technical patterns
        technical_patterns = [
            r'\b[A-Z]{2,}\b',  # Acronyms
            r'\d+\.\d+',       # Decimal numbers
            r'[a-zA-Z]+\([^)]*\)',  # Function-like patterns
            r'\b\w+[-_]\w+\b',  # Hyphenated/underscore terms
        ]
        
        for pattern in technical_patterns:
            matches = len(re.findall(pattern, text))
            complexity_score += matches * 0.1
        
        # Sentence complexity
        sentences = sent_tokenize(text)
        if sentences:
            avg_sentence_length = np.mean([len(word_tokenize(sent)) for sent in sentences])
            complexity_score += min(2.0, avg_sentence_length / 20)
        
        # Vocabulary sophistication
        words = word_tokenize(text.lower())
        if words:
            avg_word_length = np.mean([len(word) for word in words if word.isalpha()])
            complexity_score += min(1.0, avg_word_length / 8)
        
        return min(5.0, complexity_score)

class AuthenticityAnalyzer:
    """Analyzes authenticity markers in professional writing"""

    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")

        # Human authenticity markers
        self.human_markers = {
            'personal_experience': [
                r'\b(in my experience|from my perspective|I have found|I have observed)\b',
                r'\b(during my time|when I worked|in my role as)\b',
                r'\b(I remember|I recall|I witnessed)\b'
            ],
            'emotional_expression': [
                r'\b(frustrated|excited|concerned|pleased|disappointed)\b',
                r'\b(unfortunately|fortunately|surprisingly|remarkably)\b',
                r'\b(I feel|I believe|I think|I suspect)\b'
            ],
            'uncertainty_hedging': [
                r'\b(perhaps|maybe|possibly|likely|probably)\b',
                r'\b(it seems|appears to|tends to|might be)\b',
                r'\b(to some extent|in some cases|generally speaking)\b'
            ],
            'conversational_elements': [
                r'\b(by the way|speaking of|incidentally)\b',
                r'\b(as you know|as mentioned|as discussed)\b',
                r'\b(let me|let\'s|we should)\b'
            ]
        }

        # AI generation indicators
        self.ai_indicators = {
            'generic_phrases': [
                r'\b(it is important to note|it should be noted)\b',
                r'\b(in conclusion|to summarize|in summary)\b',
                r'\b(furthermore|moreover|additionally)\b'
            ],
            'overly_formal': [
                r'\b(utilize|facilitate|implement|optimize)\b',
                r'\b(comprehensive|extensive|significant|substantial)\b',
                r'\b(various|numerous|multiple|several)\b'
            ],
            'repetitive_patterns': [
                r'\b(this is|these are|there are|it is)\b',
                r'\b(can be|will be|should be|must be)\b'
            ]
        }

    def analyze_authenticity(self, text: str) -> Dict[str, Any]:
        """Analyze authenticity markers in the text"""
        text_lower = text.lower()

        # Count human markers
        human_scores = {}
        for category, patterns in self.human_markers.items():
            count = sum(len(re.findall(pattern, text_lower)) for pattern in patterns)
            human_scores[category] = count

        # Count AI indicators
        ai_scores = {}
        for category, patterns in self.ai_indicators.items():
            count = sum(len(re.findall(pattern, text_lower)) for pattern in patterns)
            ai_scores[category] = count

        # Calculate authenticity score
        total_human_markers = sum(human_scores.values())
        total_ai_indicators = sum(ai_scores.values())
        total_markers = total_human_markers + total_ai_indicators

        if total_markers == 0:
            authenticity_score = 3.0  # Neutral
        else:
            authenticity_score = (total_human_markers / total_markers) * 5.0

        # Analyze writing style consistency
        style_consistency = self._analyze_style_consistency(text)

        # Check for personal voice
        personal_voice_score = self._assess_personal_voice(text)

        return {
            'authenticity_score': authenticity_score,
            'human_markers': human_scores,
            'ai_indicators': ai_scores,
            'style_consistency': style_consistency,
            'personal_voice_score': personal_voice_score,
            'total_human_markers': total_human_markers,
            'total_ai_indicators': total_ai_indicators
        }

    def _analyze_style_consistency(self, text: str) -> float:
        """Analyze consistency in writing style"""
        doc = self.nlp(text)
        sentences = list(doc.sents)

        if len(sentences) < 3:
            return 3.0  # Not enough data

        # Analyze sentence length variation
        sentence_lengths = [len(sent) for sent in sentences]
        length_std = np.std(sentence_lengths)
        length_consistency = max(0, 5 - (length_std / 10))

        # Analyze vocabulary variation
        words_per_sentence = []
        for sent in sentences:
            words = [token.text.lower() for token in sent if token.is_alpha]
            words_per_sentence.append(len(set(words)))

        vocab_std = np.std(words_per_sentence) if words_per_sentence else 0
        vocab_consistency = max(0, 5 - (vocab_std / 5))

        return (length_consistency + vocab_consistency) / 2

    def _assess_personal_voice(self, text: str) -> float:
        """Assess the presence of personal voice in writing"""
        personal_indicators = [
            r'\b(I|my|me|mine)\b',
            r'\b(we|our|us|ours)\b',
            r'\b(you|your|yours)\b'
        ]

        personal_count = sum(len(re.findall(pattern, text.lower())) for pattern in personal_indicators)
        word_count = len(text.split())

        if word_count == 0:
            return 0.0

        personal_ratio = personal_count / word_count
        return min(5.0, personal_ratio * 50)  # Scale to 0-5

class CredibilityAnalyzer:
    """Analyzes credibility markers in professional writing"""

    def analyze_credibility(self, text: str) -> Dict[str, Any]:
        """Analyze credibility markers in the text"""

        # Citation analysis
        citations = self._count_citations(text)

        # Reference analysis
        references = self._analyze_references(text)

        # Fact-checking indicators
        fact_indicators = self._analyze_fact_indicators(text)

        # Source attribution
        source_attribution = self._analyze_source_attribution(text)

        # Calculate overall credibility score
        credibility_score = self._calculate_credibility_score(citations, references, fact_indicators, source_attribution)

        return {
            'credibility_score': credibility_score,
            'citations': citations,
            'references': references,
            'fact_indicators': fact_indicators,
            'source_attribution': source_attribution
        }

    def _count_citations(self, text: str) -> Dict[str, Any]:
        """Count and analyze citations"""
        citation_patterns = {
            'apa_style': r'\([A-Za-z]+,?\s+\d{4}\)',  # (Author, 2023)
            'numbered': r'\[\d+\]',                    # [1]
            'footnote': r'\b\d+\b',                    # Potential footnote numbers
            'et_al': r'et al\.',                       # et al.
            'ibid': r'ibid\.',                         # ibid.
        }

        citation_counts = {}
        for style, pattern in citation_patterns.items():
            citation_counts[style] = len(re.findall(pattern, text))

        total_citations = sum(citation_counts.values())

        return {
            'total_citations': total_citations,
            'by_style': citation_counts,
            'citation_density': total_citations / max(len(text.split()), 1)
        }

    def _analyze_references(self, text: str) -> Dict[str, Any]:
        """Analyze reference patterns"""
        reference_indicators = [
            r'\b(according to|as stated by|as reported by)\b',
            r'\b(research shows|studies indicate|evidence suggests)\b',
            r'\b(published in|journal of|proceedings of)\b'
        ]

        reference_count = sum(len(re.findall(pattern, text.lower())) for pattern in reference_indicators)

        return {
            'reference_count': reference_count,
            'reference_density': reference_count / max(len(text.split()), 1)
        }

    def _analyze_fact_indicators(self, text: str) -> Dict[str, Any]:
        """Analyze fact-checking and verification indicators"""
        fact_patterns = [
            r'\b(verified|confirmed|documented|established)\b',
            r'\b(data shows|statistics indicate|research demonstrates)\b',
            r'\b(peer-reviewed|peer reviewed|validated)\b'
        ]

        fact_count = sum(len(re.findall(pattern, text.lower())) for pattern in fact_patterns)

        return {
            'fact_indicator_count': fact_count,
            'fact_density': fact_count / max(len(text.split()), 1)
        }

    def _analyze_source_attribution(self, text: str) -> Dict[str, Any]:
        """Analyze source attribution patterns"""
        attribution_patterns = [
            r'\b(source:|sources:|reference:|references:)\b',
            r'\b(cited in|quoted from|adapted from)\b',
            r'\b(personal communication|private correspondence)\b'
        ]

        attribution_count = sum(len(re.findall(pattern, text.lower())) for pattern in attribution_patterns)

        return {
            'attribution_count': attribution_count,
            'attribution_density': attribution_count / max(len(text.split()), 1)
        }

    def _calculate_credibility_score(self, citations: Dict, references: Dict,
                                   fact_indicators: Dict, source_attribution: Dict) -> float:
        """Calculate overall credibility score"""
        score = 0

        # Citation score (0-2 points)
        if citations['total_citations'] > 0:
            score += min(2, citations['total_citations'] * 0.5)

        # Reference score (0-1 point)
        if references['reference_count'] > 0:
            score += min(1, references['reference_count'] * 0.3)

        # Fact indicator score (0-1 point)
        if fact_indicators['fact_indicator_count'] > 0:
            score += min(1, fact_indicators['fact_indicator_count'] * 0.3)

        # Source attribution score (0-1 point)
        if source_attribution['attribution_count'] > 0:
            score += min(1, source_attribution['attribution_count'] * 0.5)

        return score

class ProfessionalWritingAnalysisService:
    """Main service for comprehensive professional writing analysis"""

    def __init__(self):
        self.domain_analyzer = DomainExpertiseAnalyzer()
        self.authenticity_analyzer = AuthenticityAnalyzer()
        self.credibility_analyzer = CredibilityAnalyzer()
        self.nlp = spacy.load("en_core_web_sm")

    def analyze(self, text: str) -> ProfessionalAnalysisResult:
        """Perform comprehensive professional writing analysis"""

        # Domain expertise analysis
        domain_analysis = self.domain_analyzer.analyze_domain_expertise(text)

        # Authenticity analysis
        authenticity_analysis = self.authenticity_analyzer.analyze_authenticity(text)

        # Credibility analysis
        credibility_analysis = self.credibility_analyzer.analyze_credibility(text)

        # Linguistic feature analysis
        linguistic_features = self._analyze_linguistic_features(text)

        # Writing style analysis
        writing_style = self._analyze_writing_style(text)

        # Generate AI vs Human likelihood factors
        ai_factors, human_factors = self._generate_likelihood_factors(
            domain_analysis, authenticity_analysis, credibility_analysis,
            linguistic_features, writing_style
        )

        return ProfessionalAnalysisResult(
            domain_detected=domain_analysis['primary_domain'],
            expertise_level=domain_analysis['expertise_level'],
            authenticity_score=authenticity_analysis['authenticity_score'],
            professional_indicators={
                'domain_confidence': domain_analysis['domain_confidence'],
                'knowledge_depth': domain_analysis['knowledge_depth']['depth_score'],
                'technical_complexity': domain_analysis['technical_complexity'],
                'credibility_score': credibility_analysis['credibility_score']
            },
            linguistic_features=linguistic_features,
            domain_expertise=domain_analysis,
            writing_style=writing_style,
            credibility_markers=credibility_analysis,
            ai_likelihood_factors=ai_factors,
            human_likelihood_factors=human_factors
        )

    def _analyze_linguistic_features(self, text: str) -> Dict[str, float]:
        """Analyze linguistic features of the text"""
        doc = self.nlp(text)

        # Basic metrics
        word_count = len([token for token in doc if token.is_alpha])
        sentence_count = len(list(doc.sents))

        if word_count == 0 or sentence_count == 0:
            return {
                'readability_score': 0,
                'complexity_score': 0,
                'formality_score': 0,
                'coherence_score': 0,
                'vocabulary_richness': 0
            }

        # Readability metrics
        try:
            readability = flesch_reading_ease(text)
            grade_level = flesch_kincaid_grade(text)
            ari = automated_readability_index(text)
        except:
            readability = 50  # Default moderate readability
            grade_level = 10
            ari = 10

        # Complexity analysis
        avg_sentence_length = word_count / sentence_count
        complexity_score = min(5.0, (avg_sentence_length - 10) / 10 * 5)

        # Formality analysis
        formal_words = ['however', 'therefore', 'furthermore', 'moreover', 'consequently']
        formal_count = sum(1 for word in formal_words if word in text.lower())
        formality_score = min(5.0, formal_count / max(sentence_count, 1) * 5)

        # Coherence analysis (simplified)
        transition_words = ['however', 'therefore', 'furthermore', 'moreover', 'consequently',
                          'nevertheless', 'additionally', 'similarly', 'conversely']
        transition_count = sum(1 for word in transition_words if word in text.lower())
        coherence_score = min(5.0, transition_count / max(sentence_count, 1) * 10)

        # Vocabulary richness
        unique_words = len(set(token.text.lower() for token in doc if token.is_alpha))
        vocabulary_richness = unique_words / max(word_count, 1) * 5

        return {
            'readability_score': max(0, min(5, (100 - readability) / 20)),  # Invert and scale
            'complexity_score': complexity_score,
            'formality_score': formality_score,
            'coherence_score': coherence_score,
            'vocabulary_richness': vocabulary_richness,
            'grade_level': grade_level,
            'ari_score': ari
        }

    def _analyze_writing_style(self, text: str) -> Dict[str, Any]:
        """Analyze writing style characteristics"""
        doc = self.nlp(text)

        # Sentence structure analysis
        sentence_structures = self._analyze_sentence_structures(doc)

        # Voice analysis (active vs passive)
        voice_analysis = self._analyze_voice(doc)

        # Tense analysis
        tense_analysis = self._analyze_tense_usage(doc)

        # Punctuation analysis
        punctuation_analysis = self._analyze_punctuation(text)

        return {
            'sentence_structures': sentence_structures,
            'voice_analysis': voice_analysis,
            'tense_analysis': tense_analysis,
            'punctuation_analysis': punctuation_analysis
        }

    def _analyze_sentence_structures(self, doc) -> Dict[str, Any]:
        """Analyze sentence structure patterns"""
        sentences = list(doc.sents)

        if not sentences:
            return {'avg_length': 0, 'length_variation': 0, 'complexity_distribution': {}}

        lengths = [len(sent) for sent in sentences]

        # Classify sentences by complexity
        simple_sentences = sum(1 for length in lengths if length <= 15)
        compound_sentences = sum(1 for length in lengths if 15 < length <= 25)
        complex_sentences = sum(1 for length in lengths if length > 25)

        return {
            'avg_length': np.mean(lengths),
            'length_variation': np.std(lengths),
            'complexity_distribution': {
                'simple': simple_sentences / len(sentences),
                'compound': compound_sentences / len(sentences),
                'complex': complex_sentences / len(sentences)
            }
        }

    def _analyze_voice(self, doc) -> Dict[str, Any]:
        """Analyze active vs passive voice usage"""
        total_verbs = 0
        passive_verbs = 0

        for token in doc:
            if token.pos_ == 'VERB':
                total_verbs += 1
                if token.dep_ == 'auxpass':
                    passive_verbs += 1

        passive_ratio = passive_verbs / max(total_verbs, 1)

        return {
            'passive_ratio': passive_ratio,
            'active_ratio': 1 - passive_ratio,
            'total_verbs': total_verbs,
            'passive_verbs': passive_verbs
        }

    def _analyze_tense_usage(self, doc) -> Dict[str, Any]:
        """Analyze tense usage patterns"""
        tense_counts = {'past': 0, 'present': 0, 'future': 0}

        for token in doc:
            if token.pos_ == 'VERB':
                if 'Past' in token.morph.get('Tense', []):
                    tense_counts['past'] += 1
                elif 'Pres' in token.morph.get('Tense', []):
                    tense_counts['present'] += 1
                elif token.text.lower() in ['will', 'shall', 'going']:
                    tense_counts['future'] += 1

        total_tense_verbs = sum(tense_counts.values())

        if total_tense_verbs == 0:
            return {'distribution': tense_counts, 'primary_tense': 'unknown'}

        tense_distribution = {tense: count / total_tense_verbs for tense, count in tense_counts.items()}
        primary_tense = max(tense_counts, key=tense_counts.get)

        return {
            'distribution': tense_distribution,
            'primary_tense': primary_tense,
            'tense_consistency': max(tense_distribution.values())
        }

    def _analyze_punctuation(self, text: str) -> Dict[str, Any]:
        """Analyze punctuation usage patterns"""
        punctuation_counts = {
            'periods': text.count('.'),
            'commas': text.count(','),
            'semicolons': text.count(';'),
            'colons': text.count(':'),
            'exclamations': text.count('!'),
            'questions': text.count('?'),
            'quotations': text.count('"') + text.count("'"),
            'parentheses': text.count('(') + text.count(')')
        }

        total_chars = len(text)
        punctuation_density = sum(punctuation_counts.values()) / max(total_chars, 1)

        return {
            'counts': punctuation_counts,
            'density': punctuation_density,
            'variety': len([count for count in punctuation_counts.values() if count > 0])
        }

    def _generate_likelihood_factors(self, domain_analysis: Dict, authenticity_analysis: Dict,
                                   credibility_analysis: Dict, linguistic_features: Dict,
                                   writing_style: Dict) -> Tuple[List[str], List[str]]:
        """Generate factors indicating AI vs Human likelihood"""
        ai_factors = []
        human_factors = []

        # Domain expertise factors
        if domain_analysis['expertise_level'] in ['novice', 'intermediate']:
            if domain_analysis['technical_complexity'] < 2.0:
                ai_factors.append("Limited technical complexity for claimed domain expertise")
        else:
            human_factors.append(f"High expertise level ({domain_analysis['expertise_level']}) with appropriate complexity")

        # Authenticity factors
        if authenticity_analysis['authenticity_score'] < 2.0:
            ai_factors.append("Low authenticity markers, high AI indicators")
        elif authenticity_analysis['authenticity_score'] > 3.5:
            human_factors.append("Strong human authenticity markers present")

        # Credibility factors
        if credibility_analysis['credibility_score'] > 3.0:
            human_factors.append("Strong credibility markers with citations and references")
        elif credibility_analysis['credibility_score'] < 1.0:
            ai_factors.append("Lack of credibility markers and source attribution")

        # Linguistic factors
        if linguistic_features['formality_score'] > 4.0 and linguistic_features['complexity_score'] > 4.0:
            ai_factors.append("Overly formal and complex language patterns")

        if linguistic_features['vocabulary_richness'] < 2.0:
            ai_factors.append("Limited vocabulary variation")
        elif linguistic_features['vocabulary_richness'] > 4.0:
            human_factors.append("Rich and varied vocabulary usage")

        # Writing style factors
        if writing_style['voice_analysis']['passive_ratio'] > 0.7:
            ai_factors.append("Excessive use of passive voice")

        if writing_style['sentence_structures']['complexity_distribution']['simple'] > 0.8:
            ai_factors.append("Overly uniform sentence structure")
        elif writing_style['sentence_structures']['length_variation'] > 10:
            human_factors.append("Natural variation in sentence structure")

        return ai_factors, human_factors

# Command line interface for testing
if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        test_text = ' '.join(sys.argv[1:])

        analyzer = ProfessionalWritingAnalysisService()
        result = analyzer.analyze(test_text)

        print(f"Domain: {result.domain_detected}")
        print(f"Expertise Level: {result.expertise_level}")
        print(f"Authenticity Score: {result.authenticity_score:.2f}")
        print(f"Professional Indicators: {result.professional_indicators}")
        print(f"\nAI Likelihood Factors:")
        for factor in result.ai_likelihood_factors:
            print(f"  - {factor}")
        print(f"\nHuman Likelihood Factors:")
        for factor in result.human_likelihood_factors:
            print(f"  - {factor}")
    else:
        print("Usage: python professional_analyzer.py <text_to_analyze>")
        print("Example: python professional_analyzer.py 'This is a sample professional text to analyze.'")

<?php
/**
 * MySQL Diagnostic Tool for XAMPP
 * Helps identify and fix common MySQL startup issues
 */

echo "🔍 MySQL Diagnostic Tool\n";
echo "========================\n\n";

// Check if we're on Windows
$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

if (!$isWindows) {
    echo "❌ This diagnostic is designed for Windows XAMPP installations.\n";
    exit(1);
}

// Common XAMPP paths
$xamppPath = 'C:\xampp';
$mysqlPath = $xamppPath . '\mysql';
$mysqlBin = $mysqlPath . '\bin';
$mysqlData = $mysqlPath . '\data';
$mysqlLogs = $xamppPath . '\mysql\data';

echo "1. Checking XAMPP Installation...\n";
if (is_dir($xamppPath)) {
    echo "✅ XAMPP found at: $xamppPath\n";
} else {
    echo "❌ XAMPP not found at default location: $xamppPath\n";
    echo "   Please check your XAMPP installation path.\n";
    exit(1);
}

echo "\n2. Checking MySQL directories...\n";
if (is_dir($mysqlPath)) {
    echo "✅ MySQL directory found\n";
} else {
    echo "❌ MySQL directory not found\n";
}

if (is_dir($mysqlData)) {
    echo "✅ MySQL data directory found\n";
} else {
    echo "❌ MySQL data directory not found\n";
}

echo "\n3. Checking for MySQL error logs...\n";
$errorLogFiles = [
    $mysqlLogs . '\mysql_error.log',
    $mysqlLogs . '\error.log',
    $xamppPath . '\mysql\data\mysql_error.log'
];

$foundErrorLog = false;
foreach ($errorLogFiles as $logFile) {
    if (file_exists($logFile)) {
        echo "✅ Found error log: $logFile\n";
        $foundErrorLog = true;
        
        // Read last 20 lines of error log
        echo "\n📄 Last 20 lines of error log:\n";
        echo str_repeat("-", 50) . "\n";
        
        $lines = file($logFile);
        $lastLines = array_slice($lines, -20);
        foreach ($lastLines as $line) {
            echo $line;
        }
        echo str_repeat("-", 50) . "\n";
        break;
    }
}

if (!$foundErrorLog) {
    echo "⚠️ No error logs found\n";
}

echo "\n4. Checking for port conflicts...\n";
// Check if port 3306 is in use
$output = shell_exec('netstat -an | findstr :3306');
if ($output) {
    echo "⚠️ Port 3306 is in use:\n";
    echo $output . "\n";
} else {
    echo "✅ Port 3306 appears to be free\n";
}

echo "\n5. Checking MySQL configuration...\n";
$myIniFile = $mysqlPath . '\bin\my.ini';
if (file_exists($myIniFile)) {
    echo "✅ MySQL configuration file found: $myIniFile\n";
} else {
    echo "❌ MySQL configuration file not found\n";
}

echo "\n6. Checking for common issues...\n";

// Check for ibdata1 corruption
$ibdata1 = $mysqlData . '\ibdata1';
if (file_exists($ibdata1)) {
    $size = filesize($ibdata1);
    echo "✅ ibdata1 file exists (size: " . number_format($size) . " bytes)\n";
    if ($size < 1024) {
        echo "⚠️ ibdata1 file is very small - possible corruption\n";
    }
} else {
    echo "❌ ibdata1 file not found - MySQL data may be corrupted\n";
}

// Check for mysql folder in data directory
$mysqlDataDir = $mysqlData . '\mysql';
if (is_dir($mysqlDataDir)) {
    echo "✅ MySQL system database directory exists\n";
} else {
    echo "❌ MySQL system database directory missing\n";
}

echo "\n🔧 RECOMMENDED SOLUTIONS:\n";
echo "========================\n";

if ($output && strpos($output, ':3306') !== false) {
    echo "1. PORT CONFLICT DETECTED:\n";
    echo "   - Close any other MySQL services\n";
    echo "   - Check Task Manager for mysqld.exe processes\n";
    echo "   - Restart your computer if needed\n\n";
}

echo "2. GENERAL FIXES TO TRY:\n";
echo "   a) Stop all MySQL services in Task Manager\n";
echo "   b) Delete these files if they exist:\n";
echo "      - $mysqlData\\ib_logfile0\n";
echo "      - $mysqlData\\ib_logfile1\n";
echo "   c) Restart XAMPP as Administrator\n\n";

echo "3. IF MYSQL WON'T START:\n";
echo "   a) Backup your databases first!\n";
echo "   b) Try running this command in Command Prompt as Admin:\n";
echo "      cd $mysqlBin\n";
echo "      mysqld --console\n\n";

echo "4. NUCLEAR OPTION (if nothing else works):\n";
echo "   a) Backup your database files\n";
echo "   b) Reinstall XAMPP\n";
echo "   c) Restore your databases\n\n";

echo "💡 TIP: Always run XAMPP as Administrator to avoid permission issues.\n";
?>

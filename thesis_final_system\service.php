<?php
/**
 * AI Text Detector Service - Modern Interface
 * Updated with modern design and improved functionality
 */

session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

require_once 'db.php';
require_once 'ai_analysis_service.php';

// Handle analysis request
$result = null;
$error = null;
$feedback_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle feedback submission
    if (isset($_POST['feedback'])) {
        $feedback_text = trim($_POST['feedback']);
        if (!empty($feedback_text)) {
            try {
                $stmt = $conn->prepare("INSERT INTO feedback (feedback_text) VALUES (?)");
                $stmt->bind_param("s", $feedback_text);
                $stmt->execute();
                $feedback_message = "Thank you for your feedback!";
                $stmt->close();
            } catch (Exception $e) {
                $feedback_message = "Failed to submit feedback: " . $e->getMessage();
            }
        } else {
            $feedback_message = "Feedback cannot be empty.";
        }
    }

    // Handle analysis request
    if (isset($_POST['analyze'])) {
        $inputText = trim($_POST['text'] ?? '');

        if (empty($inputText)) {
            $error = "Please enter some text to analyze.";
        } else {
            // Word count check
            $wordCount = str_word_count($inputText);
            if ($wordCount > 2000) {
                $error = "Text exceeds maximum limit of 2000 words. Current: $wordCount words.";
            } else {
                // Use the AI Analysis Service
                try {
                    $aiService = new AIAnalysisService();
                    $analysisResult = $aiService->analyzeText($inputText);

                    if (isset($analysisResult['classification'])) {
                        $result = $analysisResult;
                    } else {
                        $error = "Analysis failed: " . ($analysisResult['error'] ?? 'Unknown error');
                    }
                } catch (Exception $e) {
                    $error = "Analysis service error: " . $e->getMessage();
                }
            }
        }
    }

}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Text Detector - Analysis Service</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .logo i {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: background 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .main-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .card-subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }

        .card-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-textarea {
            width: 100%;
            min-height: 200px;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #cbd5e1;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1px solid;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
        }

        .result-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 1rem;
            padding: 2rem;
            margin-top: 2rem;
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .result-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .result-icon.ai {
            background: #fef2f2;
            color: #dc2626;
        }

        .result-icon.human {
            background: #f0fdf4;
            color: #16a34a;
        }

        .result-icon.uncertain {
            background: #fffbeb;
            color: #d97706;
        }

        .result-title {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .confidence-bar {
            background: #e5e7eb;
            height: 0.5rem;
            border-radius: 0.25rem;
            overflow: hidden;
            margin: 1rem 0;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.5s ease;
        }

        .confidence-fill.high {
            background: linear-gradient(90deg, #16a34a, #22c55e);
        }

        .confidence-fill.medium {
            background: linear-gradient(90deg, #d97706, #f59e0b);
        }

        .confidence-fill.low {
            background: linear-gradient(90deg, #dc2626, #ef4444);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .reasoning {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e5e7eb;
            margin-top: 1.5rem;
        }

        .reasoning h4 {
            margin-bottom: 0.75rem;
            color: #374151;
        }

        .word-count {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .feedback-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .card-body {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-brain"></i>
                <span>AI Text Detector</span>
            </div>
            <div class="user-info">
                <span>Welcome, <?= htmlspecialchars($_SESSION['username']) ?></span>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <div class="main-card">
            <div class="card-header">
                <h1 class="card-title">AI Text Detection Service</h1>
                <p class="card-subtitle">Analyze text to determine if it was generated by AI or written by humans</p>
            </div>

            <div class="card-body">
                <!-- Error/Success Messages -->
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($feedback_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($feedback_message) ?>
                    </div>
                <?php endif; ?>

                <!-- Analysis Form -->
                <form method="post" action="">
                    <div class="form-group">
                        <label for="text" class="form-label">
                            <i class="fas fa-edit"></i>
                            Text to Analyze
                        </label>
                        <textarea
                            id="text"
                            name="text"
                            class="form-textarea"
                            placeholder="Paste or type the text you want to analyze here..."
                            required
                        ><?= isset($_POST['text']) ? htmlspecialchars($_POST['text']) : '' ?></textarea>
                        <div class="word-count">
                            <span id="wordCount">0</span> words (max 2000)
                        </div>
                    </div>

                    <button type="submit" name="analyze" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Analyze Text
                    </button>
                </form>

                <!-- Analysis Results -->
                <?php if ($result): ?>
                    <div class="result-card">
                        <div class="result-header">
                            <?php
                            $classification = strtoupper($result['classification']);
                            $confidence = floatval($result['confidence']);
                            $iconClass = 'uncertain';
                            $resultText = 'Uncertain';

                            if ($classification === 'AI_GENERATED') {
                                $iconClass = 'ai';
                                $resultText = 'AI Generated';
                            } elseif ($classification === 'HUMAN_WRITTEN') {
                                $iconClass = 'human';
                                $resultText = 'Human Written';
                            }
                            ?>
                            <div class="result-icon <?= $iconClass ?>">
                                <i class="fas fa-<?= $iconClass === 'ai' ? 'robot' : ($iconClass === 'human' ? 'user' : 'question') ?>"></i>
                            </div>
                            <div>
                                <h3 class="result-title"><?= $resultText ?></h3>
                                <p>Confidence: <?= number_format($confidence * 100, 1) ?>%</p>
                            </div>
                        </div>

                        <!-- Confidence Bar -->
                        <div class="confidence-bar">
                            <?php
                            $barClass = 'low';
                            if ($confidence >= 0.7) $barClass = 'high';
                            elseif ($confidence >= 0.4) $barClass = 'medium';
                            ?>
                            <div class="confidence-fill <?= $barClass ?>" style="width: <?= $confidence * 100 ?>%"></div>
                        </div>

                        <!-- Statistics -->
                        <div class="stats-grid">
                            <?php if (isset($result['sub_scores'])): ?>
                                <?php foreach ($result['sub_scores'] as $metric => $score): ?>
                                    <div class="stat-item">
                                        <div class="stat-label"><?= ucwords(str_replace('_', ' ', $metric)) ?></div>
                                        <div class="stat-value"><?= number_format($score * 100, 1) ?>%</div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <?php if (isset($result['processing_time'])): ?>
                                <div class="stat-item">
                                    <div class="stat-label">Processing Time</div>
                                    <div class="stat-value"><?= number_format($result['processing_time'], 3) ?>s</div>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($result['model_used'])): ?>
                                <div class="stat-item">
                                    <div class="stat-label">Model Used</div>
                                    <div class="stat-value"><?= htmlspecialchars($result['model_used']) ?></div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Reasoning -->
                        <?php if (isset($result['reasoning'])): ?>
                            <div class="reasoning">
                                <h4><i class="fas fa-lightbulb"></i> Analysis Reasoning</h4>
                                <p><?= htmlspecialchars($result['reasoning']) ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Fallback Notice -->
                        <?php if (isset($result['fallback']) && $result['fallback']): ?>
                            <div class="alert alert-error" style="margin-top: 1rem;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Note:</strong> This analysis used a fallback method due to technical issues. Results may be less accurate.
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Feedback Section -->
                <div class="feedback-section">
                    <h3><i class="fas fa-comment"></i> Feedback</h3>
                    <p>Help us improve by sharing your thoughts about the analysis results.</p>

                    <form method="post" action="" style="margin-top: 1rem;">
                        <div class="form-group">
                            <textarea
                                name="feedback"
                                class="form-textarea"
                                placeholder="Share your feedback here..."
                                style="min-height: 100px;"
                            ></textarea>
                        </div>
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-paper-plane"></i>
                            Submit Feedback
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Word count functionality
        const textarea = document.getElementById('text');
        const wordCountSpan = document.getElementById('wordCount');

        function updateWordCount() {
            const text = textarea.value.trim();
            const wordCount = text === '' ? 0 : text.split(/\s+/).length;
            wordCountSpan.textContent = wordCount;

            // Change color based on limit
            if (wordCount > 2000) {
                wordCountSpan.style.color = '#dc2626';
            } else if (wordCount > 1800) {
                wordCountSpan.style.color = '#d97706';
            } else {
                wordCountSpan.style.color = '#6b7280';
            }
        }

        textarea.addEventListener('input', updateWordCount);

        // Initial word count
        updateWordCount();

        // Auto-resize textarea
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.max(200, this.scrollHeight) + 'px';
        });
    </script>
</body>
</html>

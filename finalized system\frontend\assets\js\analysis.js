/**
 * Enhanced AI Detection System - Analysis Module
 * Text analysis functionality and UI interactions
 */

class TextAnalyzer {
    constructor(app) {
        this.app = app;
        this.currentText = '';
        this.currentMethod = 'text';
        this.analysisOptions = {
            professionalAnalysis: true,
            domainDetection: true,
            detailedBreakdown: true,
            ensembleAnalysis: true
        };
        
        this.init();
    }
    
    init() {
        this.setupInputMethods();
        this.setupTextInput();
        this.setupFileUpload();
        this.setupUrlExtraction();
        this.setupAnalysisOptions();
        this.setupAnalysisButton();
    }
    
    setupInputMethods() {
        const methodButtons = document.querySelectorAll('.method-btn');
        
        methodButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const method = btn.dataset.method;
                this.switchInputMethod(method);
                this.updateActiveMethodButton(btn);
            });
        });
    }
    
    switchInputMethod(method) {
        this.currentMethod = method;
        
        // Hide all panels
        document.querySelectorAll('.input-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Show selected panel
        const targetPanel = document.getElementById(`${method}-input-panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }
        
        // Clear previous content
        this.clearCurrentInput();
    }
    
    updateActiveMethodButton(activeBtn) {
        document.querySelectorAll('.method-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        activeBtn.classList.add('active');
    }
    
    setupTextInput() {
        const textInput = document.getElementById('text-input');
        const charCount = document.getElementById('char-count');
        const wordCount = document.getElementById('word-count');
        
        if (textInput) {
            textInput.addEventListener('input', (e) => {
                this.currentText = e.target.value;
                this.updateTextStats(this.currentText, charCount, wordCount);
            });
            
            textInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    this.currentText = e.target.value;
                    this.updateTextStats(this.currentText, charCount, wordCount);
                }, 10);
            });
        }
    }
    
    setupFileUpload() {
        const fileInput = document.getElementById('file-input');
        const dropZone = document.getElementById('file-drop-zone');
        const uploadedFiles = document.getElementById('uploaded-files');
        
        if (fileInput && dropZone) {
            // File input change
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files);
            });
            
            // Drag and drop
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                this.handleFiles(e.dataTransfer.files);
            });
            
            // Click to browse
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });
        }
    }
    
    setupUrlExtraction() {
        const urlInput = document.getElementById('url-input');
        const extractBtn = document.getElementById('extract-url-btn');
        const urlPreview = document.getElementById('url-preview');
        
        if (extractBtn) {
            extractBtn.addEventListener('click', async () => {
                const url = urlInput.value.trim();
                if (!url) {
                    this.app.showError('Please enter a valid URL');
                    return;
                }
                
                try {
                    await this.extractTextFromUrl(url, urlPreview);
                } catch (error) {
                    this.app.showError('Failed to extract text from URL');
                    console.error(error);
                }
            });
        }
    }
    
    setupAnalysisOptions() {
        const optionInputs = document.querySelectorAll('.analysis-options input[type="checkbox"]');
        
        optionInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const option = e.target.id.replace('-', '');
                this.analysisOptions[option] = e.target.checked;
            });
        });
    }
    
    setupAnalysisButton() {
        const analyzeBtn = document.getElementById('analyze-btn');
        
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', async () => {
                await this.performAnalysis();
            });
        }
    }
    
    updateTextStats(text, charElement, wordElement) {
        const charCount = text.length;
        const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
        
        if (charElement) charElement.textContent = `${charCount} characters`;
        if (wordElement) wordElement.textContent = `${wordCount} words`;
    }
    
    async handleFiles(files) {
        const uploadedFiles = document.getElementById('uploaded-files');
        if (!uploadedFiles) return;
        
        uploadedFiles.innerHTML = '';
        
        for (const file of files) {
            if (this.isValidFileType(file)) {
                const fileItem = this.createFileItem(file);
                uploadedFiles.appendChild(fileItem);
                
                try {
                    const text = await this.extractTextFromFile(file);
                    this.currentText = text;
                    this.updateFileItemStatus(fileItem, 'success', `${text.length} characters extracted`);
                } catch (error) {
                    this.updateFileItemStatus(fileItem, 'error', 'Failed to extract text');
                    console.error('File extraction error:', error);
                }
            } else {
                this.app.showError(`Unsupported file type: ${file.name}`);
            }
        }
    }
    
    isValidFileType(file) {
        const validTypes = [
            'text/plain',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        return validTypes.includes(file.type) || file.name.endsWith('.txt');
    }
    
    createFileItem(file) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <div class="file-info">
                <div class="file-icon">
                    <i class="fas fa-${this.getFileIcon(file)}"></i>
                </div>
                <div class="file-details">
                    <h4>${file.name}</h4>
                    <p>${this.formatFileSize(file.size)} • Processing...</p>
                </div>
            </div>
            <div class="file-actions">
                <button class="btn btn-sm btn-secondary" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        return fileItem;
    }
    
    updateFileItemStatus(fileItem, status, message) {
        const details = fileItem.querySelector('.file-details p');
        if (details) {
            const size = details.textContent.split(' • ')[0];
            details.textContent = `${size} • ${message}`;
            
            if (status === 'error') {
                fileItem.classList.add('error');
            } else if (status === 'success') {
                fileItem.classList.add('success');
            }
        }
    }
    
    async extractTextFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                    resolve(e.target.result);
                } else {
                    // For other file types, we would need server-side processing
                    // For now, we'll simulate the extraction
                    resolve(`[Extracted text from ${file.name}]\n\nThis is simulated text extraction. In a real implementation, this would be processed server-side to extract text from PDF, DOC, and DOCX files.`);
                }
            };
            
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }
    
    async extractTextFromUrl(url, previewElement) {
        previewElement.innerHTML = '<div class="loading">Extracting text from URL...</div>';
        
        try {
            // In a real implementation, this would call a server-side service
            // For now, we'll simulate the extraction
            setTimeout(() => {
                const simulatedText = `[Extracted text from ${url}]\n\nThis is simulated text extraction from a web page. In a real implementation, this would fetch and parse the webpage content to extract readable text.`;
                this.currentText = simulatedText;
                
                previewElement.innerHTML = `
                    <div class="url-success">
                        <i class="fas fa-check-circle"></i>
                        <p>Successfully extracted ${simulatedText.length} characters from URL</p>
                        <div class="text-preview">${simulatedText.substring(0, 200)}...</div>
                    </div>
                `;
            }, 1500);
            
        } catch (error) {
            previewElement.innerHTML = `
                <div class="url-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>Failed to extract text from URL</p>
                </div>
            `;
            throw error;
        }
    }
    
    async performAnalysis() {
        if (!this.currentText.trim()) {
            this.app.showError('Please provide text to analyze');
            return;
        }
        
        if (this.currentText.length < 50) {
            this.app.showError('Text must be at least 50 characters long for accurate analysis');
            return;
        }
        
        try {
            this.app.showLoading();
            this.setAnalysisButtonState(true);
            
            const analysisData = {
                text: this.currentText,
                sessionId: this.app.sessionId,
                options: this.analysisOptions,
                method: this.currentMethod
            };
            
            const result = await this.app.apiCall('/api/analyze', analysisData, 'POST');
            this.displayAnalysisResults(result);
            
            // Save to history if auto-save is enabled
            const preferences = this.app.getUserPreferences();
            if (preferences.autoSave) {
                this.app.analysisHistory.unshift(result);
            }
            
            this.app.showSuccess('Analysis completed successfully');
            
        } catch (error) {
            console.error('Analysis failed:', error);
            this.app.showError('Analysis failed. Please try again.');
        } finally {
            this.app.hideLoading();
            this.setAnalysisButtonState(false);
        }
    }
    
    displayAnalysisResults(result) {
        const resultsContainer = document.getElementById('analysis-results');
        if (!resultsContainer) return;
        
        resultsContainer.innerHTML = `
            <div class="results-content fade-in">
                ${this.generateResultsHTML(result)}
            </div>
        `;
    }
    
    generateResultsHTML(result) {
        return `
            <div class="result-header">
                <div class="result-classification">
                    <div class="classification-badge ${this.app.getClassificationClass(result.classification)}">
                        ${result.classification}
                    </div>
                    <h3>Analysis Results</h3>
                </div>
                <div class="confidence-score">${Math.round(result.confidence * 100)}%</div>
            </div>
            
            ${this.generateScoreBreakdown(result.scores)}
            ${this.generateProfessionalAnalysis(result.professionalAnalysis)}
            ${this.generateReasoningSection(result.reasoning)}
            
            <div class="result-actions">
                <button class="btn btn-primary" onclick="textAnalyzer.exportResults()">
                    <i class="fas fa-download"></i>
                    Export Results
                </button>
                <button class="btn btn-secondary" onclick="textAnalyzer.shareResults()">
                    <i class="fas fa-share"></i>
                    Share
                </button>
                <button class="btn btn-secondary" onclick="textAnalyzer.saveToHistory()">
                    <i class="fas fa-bookmark"></i>
                    Save to History
                </button>
            </div>
        `;
    }
    
    generateScoreBreakdown(scores) {
        if (!scores) return '';
        
        return `
            <div class="score-breakdown">
                ${Object.entries(scores).map(([key, value]) => `
                    <div class="score-item">
                        <h4>${this.formatScoreLabel(key)}</h4>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${value * 100}%"></div>
                        </div>
                        <div class="score-value">${Math.round(value * 100)}%</div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    generateProfessionalAnalysis(analysis) {
        if (!analysis) return '';
        
        return `
            <div class="professional-analysis">
                <h4>Professional Writing Analysis</h4>
                <div class="analysis-grid">
                    <div class="analysis-metric">
                        <div class="metric-value">${analysis.domain || 'General'}</div>
                        <div class="metric-label">Domain</div>
                    </div>
                    <div class="analysis-metric">
                        <div class="metric-value">${analysis.expertiseLevel || 'N/A'}</div>
                        <div class="metric-label">Expertise Level</div>
                    </div>
                    <div class="analysis-metric">
                        <div class="metric-value">${Math.round((analysis.credibilityScore || 0) * 100)}%</div>
                        <div class="metric-label">Credibility</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateReasoningSection(reasoning) {
        if (!reasoning || !reasoning.length) return '';
        
        return `
            <div class="reasoning-section">
                <h4>Analysis Reasoning</h4>
                <ul class="reasoning-list">
                    ${reasoning.map(reason => `<li>${reason}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    // Utility methods
    clearCurrentInput() {
        this.currentText = '';
        
        const textInput = document.getElementById('text-input');
        if (textInput) textInput.value = '';
        
        const uploadedFiles = document.getElementById('uploaded-files');
        if (uploadedFiles) uploadedFiles.innerHTML = '';
        
        const urlPreview = document.getElementById('url-preview');
        if (urlPreview) urlPreview.innerHTML = '';
        
        const urlInput = document.getElementById('url-input');
        if (urlInput) urlInput.value = '';
    }
    
    setAnalysisButtonState(analyzing) {
        const btn = document.getElementById('analyze-btn');
        if (!btn) return;
        
        if (analyzing) {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
        } else {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-search"></i> Analyze Text';
        }
    }
    
    getFileIcon(file) {
        if (file.type.includes('pdf')) return 'file-pdf';
        if (file.type.includes('word')) return 'file-word';
        return 'file-text';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatScoreLabel(key) {
        return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }
    
    exportResults() {
        // Implementation for exporting results
        this.app.showSuccess('Export functionality will be implemented');
    }
    
    shareResults() {
        // Implementation for sharing results
        this.app.showSuccess('Share functionality will be implemented');
    }
    
    saveToHistory() {
        // Implementation for saving to history
        this.app.showSuccess('Saved to analysis history');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.app) {
        window.textAnalyzer = new TextAnalyzer(window.app);
    }
});

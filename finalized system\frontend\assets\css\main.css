/* Enhanced AI Detection System - Main Styles */

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --white: #0f172a;
    --gray-50: #1e293b;
    --gray-100: #334155;
    --gray-200: #475569;
    --gray-300: #64748b;
    --gray-400: #94a3b8;
    --gray-500: #cbd5e1;
    --gray-600: #e2e8f0;
    --gray-700: #f1f5f9;
    --gray-800: #f8fafc;
    --gray-900: #ffffff;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-4);
    color: var(--gray-600);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.grid {
    display: grid;
    gap: var(--spacing-6);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
}

/* Navigation */
.navbar {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 64px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--gray-900);
}

.nav-brand i {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    color: var(--gray-600);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    position: relative;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.user-menu {
    position: relative;
}

.user-name {
    font-weight: 500;
    color: var(--gray-900);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.user-menu:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-100);
}

.user-dropdown a:last-child {
    border-bottom: none;
}

.user-dropdown a:hover {
    background: var(--gray-50);
}

/* Main Content */
.main-content {
    margin-top: 64px;
    min-height: calc(100vh - 64px);
    padding: var(--spacing-8) var(--spacing-6);
}

.content-section {
    display: none;
    max-width: 1400px;
    margin: 0 auto;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: var(--spacing-8);
}

.section-header h1 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: 0;
}

/* Cards */
.card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.card-content {
    padding: var(--spacing-6);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-base);
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-200);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

/* Form Elements */
input, textarea, select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: border-color var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

textarea {
    resize: vertical;
    min-height: 120px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-xl);
}

.loading-spinner i {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.loading-spinner p {
    margin: 0;
    color: var(--gray-600);
    font-weight: 500;
}

/* Notifications */
.notification {
    position: fixed;
    top: 80px;
    right: var(--spacing-6);
    max-width: 400px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 9998;
    animation: slideInRight 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
}

.notification-content i {
    font-size: var(--font-size-lg);
}

.notification-error {
    border-left: 4px solid var(--danger-color);
}

.notification-error i {
    color: var(--danger-color);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-success i {
    color: var(--success-color);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification-info i {
    color: var(--primary-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
    margin-left: auto;
}

.notification-close:hover {
    color: var(--gray-600);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Analysis Modal */
.analysis-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.analysis-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.modal-content {
    position: relative;
    max-width: 600px;
    margin: 5% auto;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-6);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: var(--spacing-6) var(--spacing-4);
    }

    .nav-container {
        padding: 0 var(--spacing-4);
    }

    .notification {
        right: var(--spacing-4);
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .main-content {
        padding: var(--spacing-4) var(--spacing-3);
    }

    .section-header h1 {
        font-size: var(--font-size-2xl);
    }

    .card-header, .card-content {
        padding: var(--spacing-4);
    }

    .notification {
        right: var(--spacing-3);
        left: var(--spacing-3);
        max-width: none;
    }

    .modal-content {
        margin: 10% var(--spacing-4);
        max-width: none;
    }
}

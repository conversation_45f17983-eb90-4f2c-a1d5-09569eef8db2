<?php
/**
 * Enhanced AI Text Detection System Configuration
 * Comprehensive configuration for the finalized system
 */

class EnhancedConfig {
    private static $config = [
        // Database Configuration
        'database' => [
            'host' => 'localhost',
            'port' => 3306,
            'name' => 'enhanced_ai_detector',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ],

        // AI Models Configuration
        'ai_models' => [
            'primary' => [
                'provider' => 'openai',
                'model' => 'gpt-4o',
                'api_key_env' => 'OPENAI_API_KEY',
                'max_tokens' => 2000,
                'temperature' => 0.2,
                'timeout' => 60
            ],
            'secondary' => [
                'provider' => 'openai',
                'model' => 'gpt-4o-mini',
                'api_key_env' => 'OPENAI_API_KEY',
                'max_tokens' => 1500,
                'temperature' => 0.3,
                'timeout' => 45
            ],
            'ensemble' => [
                'enabled' => true,
                'models' => ['primary', 'secondary'],
                'voting_strategy' => 'weighted',
                'weights' => [0.7, 0.3]
            ]
        ],

        // Professional Writing Analysis
        'professional_analysis' => [
            'enabled' => true,
            'domain_detection' => true,
            'expertise_scoring' => true,
            'citation_analysis' => true,
            'terminology_checking' => true,
            'structure_analysis' => true
        ],

        // Machine Learning Pipeline
        'ml_pipeline' => [
            'enabled' => true,
            'training_data_path' => 'ml_pipeline/data/',
            'models_path' => 'ml_pipeline/models/',
            'feature_extraction' => [
                'linguistic_features' => true,
                'stylometric_features' => true,
                'semantic_features' => true,
                'syntactic_features' => true
            ],
            'ensemble_models' => [
                'random_forest' => true,
                'svm' => true,
                'neural_network' => true,
                'gradient_boosting' => true
            ]
        ],

        // Security Configuration
        'security' => [
            'api_rate_limit' => [
                'enabled' => true,
                'requests_per_minute' => 60,
                'requests_per_hour' => 1000,
                'burst_limit' => 10
            ],
            'authentication' => [
                'session_timeout' => 3600,
                'password_min_length' => 8,
                'require_2fa' => false,
                'max_login_attempts' => 5,
                'lockout_duration' => 900
            ],
            'encryption' => [
                'algorithm' => 'AES-256-GCM',
                'key_rotation_days' => 30
            ]
        ],

        // Performance Configuration
        'performance' => [
            'caching' => [
                'enabled' => true,
                'driver' => 'redis',
                'ttl' => 3600,
                'analysis_cache_ttl' => 7200
            ],
            'optimization' => [
                'batch_processing' => true,
                'async_analysis' => true,
                'result_compression' => true
            ]
        ],

        // Logging Configuration
        'logging' => [
            'level' => 'INFO',
            'channels' => [
                'analysis' => 'logs/analysis.log',
                'security' => 'logs/security.log',
                'performance' => 'logs/performance.log',
                'errors' => 'logs/errors.log',
                'api' => 'logs/api.log'
            ],
            'rotation' => [
                'enabled' => true,
                'max_size' => '10MB',
                'max_files' => 30
            ]
        ],

        // Analytics Configuration
        'analytics' => [
            'enabled' => true,
            'real_time_metrics' => true,
            'performance_tracking' => true,
            'accuracy_monitoring' => true,
            'user_behavior_tracking' => true,
            'export_formats' => ['json', 'csv', 'pdf']
        ],

        // File Processing
        'file_processing' => [
            'max_file_size' => '10MB',
            'allowed_types' => ['txt', 'docx', 'pdf', 'rtf'],
            'batch_processing' => [
                'enabled' => true,
                'max_files' => 50,
                'max_total_size' => '100MB'
            ]
        ],

        // API Configuration
        'api' => [
            'version' => 'v2',
            'base_url' => '/api/v2',
            'documentation' => true,
            'cors' => [
                'enabled' => true,
                'allowed_origins' => ['*'],
                'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                'allowed_headers' => ['Content-Type', 'Authorization']
            ]
        ]
    ];

    public static function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    public static function set($key, $value) {
        $keys = explode('.', $key);
        $config = &self::$config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }

    public static function getAll() {
        return self::$config;
    }

    public static function loadFromEnv() {
        // Load environment-specific configurations
        if (isset($_ENV['DB_HOST'])) {
            self::set('database.host', $_ENV['DB_HOST']);
        }
        if (isset($_ENV['DB_NAME'])) {
            self::set('database.name', $_ENV['DB_NAME']);
        }
        if (isset($_ENV['DB_USER'])) {
            self::set('database.username', $_ENV['DB_USER']);
        }
        if (isset($_ENV['DB_PASS'])) {
            self::set('database.password', $_ENV['DB_PASS']);
        }
    }
}

// Load environment configurations
EnhancedConfig::loadFromEnv();
?>

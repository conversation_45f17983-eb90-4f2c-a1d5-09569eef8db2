@echo off
echo 🔧 Force MySQL Restart Script
echo =============================
echo.

echo Stopping all MySQL services and processes...
net stop mysql 2>nul
net stop "MySQL80" 2>nul
net stop "MySQL57" 2>nul

echo Killing MySQL processes...
taskkill /F /IM mysqld.exe 2>nul
taskkill /F /IM mysql.exe 2>nul

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Checking if port 3306 is free...
netstat -an | findstr :3306
if %errorlevel% == 0 (
    echo Port 3306 is still in use - you may need to restart your computer
) else (
    echo Port 3306 is now free!
)

echo.
echo Now try starting MySQL from XAMPP Control Panel (run as Administrator)
echo.
pause

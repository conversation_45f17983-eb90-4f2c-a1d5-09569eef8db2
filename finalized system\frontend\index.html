<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced AI Text Detection System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-brain"></i>
                <span>Enhanced AI Detector</span>
            </div>
            
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="#analyze" class="nav-link" data-section="analyze">
                    <i class="fas fa-search"></i>
                    Analyze Text
                </a>
                <a href="#batch" class="nav-link" data-section="batch">
                    <i class="fas fa-layer-group"></i>
                    Batch Processing
                </a>
                <a href="#analytics" class="nav-link" data-section="analytics">
                    <i class="fas fa-chart-line"></i>
                    Analytics
                </a>
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
            
            <div class="nav-user">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-menu">
                    <span class="user-name">Research User</span>
                    <div class="user-dropdown">
                        <a href="#profile"><i class="fas fa-user-edit"></i> Profile</a>
                        <a href="#logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="section-header">
                <h1>Dashboard</h1>
                <p>Overview of your AI detection activities and system performance</p>
            </div>
            
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-text"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-analyses">0</h3>
                        <p>Total Analyses</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="ai-detected">0</h3>
                        <p>AI-Generated Detected</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="professional-detected">0</h3>
                        <p>Professional Writing</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="avg-confidence">0%</h3>
                        <p>Average Confidence</p>
                    </div>
                </div>
            </div>
            
            <!-- Recent Analyses -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Analyses</h3>
                        <a href="#analyze" class="btn btn-primary btn-sm">New Analysis</a>
                    </div>
                    <div class="card-content">
                        <div class="recent-analyses" id="recent-analyses">
                            <!-- Recent analyses will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Detection Accuracy Trends</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="accuracy-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Text Analysis Section -->
        <section id="analyze" class="content-section">
            <div class="section-header">
                <h1>Text Analysis</h1>
                <p>Analyze text for AI generation patterns and professional writing characteristics</p>
            </div>
            
            <div class="analysis-container">
                <div class="analysis-input">
                    <div class="input-methods">
                        <button class="method-btn active" data-method="text">
                            <i class="fas fa-keyboard"></i>
                            Type Text
                        </button>
                        <button class="method-btn" data-method="file">
                            <i class="fas fa-file-upload"></i>
                            Upload File
                        </button>
                        <button class="method-btn" data-method="url">
                            <i class="fas fa-link"></i>
                            From URL
                        </button>
                    </div>
                    
                    <!-- Text Input -->
                    <div class="input-panel active" id="text-input-panel">
                        <textarea 
                            id="text-input" 
                            placeholder="Paste or type the text you want to analyze..."
                            rows="12"
                        ></textarea>
                        <div class="input-stats">
                            <span id="char-count">0 characters</span>
                            <span id="word-count">0 words</span>
                        </div>
                    </div>
                    
                    <!-- File Upload -->
                    <div class="input-panel" id="file-input-panel">
                        <div class="file-drop-zone" id="file-drop-zone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag and drop files here or click to browse</p>
                            <input type="file" id="file-input" accept=".txt,.doc,.docx,.pdf" multiple>
                            <div class="supported-formats">
                                Supported: TXT, DOC, DOCX, PDF
                            </div>
                        </div>
                        <div class="uploaded-files" id="uploaded-files"></div>
                    </div>
                    
                    <!-- URL Input -->
                    <div class="input-panel" id="url-input-panel">
                        <div class="url-input-group">
                            <input type="url" id="url-input" placeholder="Enter URL to extract text from...">
                            <button class="btn btn-secondary" id="extract-url-btn">
                                <i class="fas fa-download"></i>
                                Extract
                            </button>
                        </div>
                        <div class="url-preview" id="url-preview"></div>
                    </div>
                    
                    <!-- Analysis Options -->
                    <div class="analysis-options">
                        <h4>Analysis Options</h4>
                        <div class="options-grid">
                            <label class="option-item">
                                <input type="checkbox" id="professional-analysis" checked>
                                <span class="checkmark"></span>
                                Professional Writing Analysis
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="domain-detection" checked>
                                <span class="checkmark"></span>
                                Domain Expertise Detection
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="detailed-breakdown" checked>
                                <span class="checkmark"></span>
                                Detailed Score Breakdown
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="ensemble-analysis" checked>
                                <span class="checkmark"></span>
                                Multi-Model Ensemble
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary btn-large" id="analyze-btn">
                        <i class="fas fa-search"></i>
                        Analyze Text
                    </button>
                </div>
                
                <!-- Analysis Results -->
                <div class="analysis-results" id="analysis-results">
                    <div class="results-placeholder">
                        <i class="fas fa-chart-pie"></i>
                        <h3>Analysis Results</h3>
                        <p>Results will appear here after analysis</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Batch Processing Section -->
        <section id="batch" class="content-section">
            <div class="section-header">
                <h1>Batch Processing</h1>
                <p>Process multiple texts simultaneously for efficient analysis</p>
            </div>
            
            <div class="batch-container">
                <div class="batch-upload">
                    <div class="upload-area">
                        <i class="fas fa-layer-group"></i>
                        <h3>Upload Multiple Files</h3>
                        <p>Select multiple files for batch analysis</p>
                        <input type="file" id="batch-files" multiple accept=".txt,.doc,.docx,.pdf">
                        <button class="btn btn-primary" onclick="document.getElementById('batch-files').click()">
                            <i class="fas fa-upload"></i>
                            Select Files
                        </button>
                    </div>
                    
                    <div class="batch-options">
                        <h4>Batch Options</h4>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="batch-professional" checked>
                                Professional Analysis
                            </label>
                            <label>
                                <input type="checkbox" id="batch-export" checked>
                                Export Results
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="batch-queue" id="batch-queue">
                    <h3>Processing Queue</h3>
                    <div class="queue-list" id="queue-list">
                        <!-- Queue items will be added here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Analytics Section -->
        <section id="analytics" class="content-section">
            <div class="section-header">
                <h1>Analytics & Reports</h1>
                <p>Detailed insights into detection patterns and system performance</p>
            </div>
            
            <div class="analytics-dashboard">
                <div class="analytics-filters">
                    <select id="time-range">
                        <option value="7d">Last 7 days</option>
                        <option value="30d">Last 30 days</option>
                        <option value="90d">Last 90 days</option>
                        <option value="1y">Last year</option>
                    </select>
                    
                    <select id="analysis-type">
                        <option value="all">All Types</option>
                        <option value="ai">AI-Generated</option>
                        <option value="human">Human-Written</option>
                        <option value="professional">Professional</option>
                    </select>
                    
                    <button class="btn btn-secondary" id="export-analytics">
                        <i class="fas fa-download"></i>
                        Export Report
                    </button>
                </div>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Detection Distribution</h3>
                        <canvas id="distribution-chart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Confidence Trends</h3>
                        <canvas id="confidence-chart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Domain Analysis</h3>
                        <canvas id="domain-chart"></canvas>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Model Performance</h3>
                        <div class="performance-metrics" id="performance-metrics">
                            <!-- Performance metrics will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="section-header">
                <h1>Settings</h1>
                <p>Configure your analysis preferences and system settings</p>
            </div>
            
            <div class="settings-container">
                <div class="settings-group">
                    <h3>Analysis Preferences</h3>
                    <div class="setting-item">
                        <label>Default Analysis Mode</label>
                        <select id="default-mode">
                            <option value="comprehensive">Comprehensive Analysis</option>
                            <option value="quick">Quick Analysis</option>
                            <option value="professional">Professional Focus</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label>Confidence Threshold</label>
                        <input type="range" id="confidence-threshold" min="0.5" max="1.0" step="0.05" value="0.7">
                        <span id="threshold-value">0.7</span>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>Interface Preferences</h3>
                    <div class="setting-item">
                        <label>Theme</label>
                        <select id="theme-select">
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="auto">Auto</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="auto-save">
                            Auto-save analysis results
                        </label>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-brain fa-spin"></i>
            <p>Analyzing text...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/analysis.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/batch.js"></script>
    <script src="assets/js/analytics.js"></script>
</body>
</html>
